<template>
    <view class="duration-coupon-page">
        <!-- 自定义导航栏 -->
        <!-- <view class="custom-navbar">
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<text class="ri-arrow-left-line back-icon"></text>
				</view>
				<view class="navbar-title">时长抵扣券</view>
				<view class="navbar-right">
					<text class="ri-question-line help-icon" @click="showHelp"></text>
				</view>
			</view>
		</view> -->

        <!-- 筛选标签 - 固定在导航栏下方 -->
        <view class="filter-tabs">
            <wd-tabs v-model="activeTab" @change="handleTabChange" line-width="50rpx" line-height="4rpx"
                color="#FF9500" sticky>
                <wd-tab title="全部" name="all"
                    :badge-props="totalCoupons > 0 ? { modelValue: totalCoupons, right: '-8px' } : null">
                </wd-tab>
                <wd-tab title="可使用" name="available"
                    :badge-props="availableCoupons > 0 ? { modelValue: availableCoupons, right: '-8px' } : null">
                </wd-tab>
                <wd-tab title="已使用" name="used"></wd-tab>
                <wd-tab title="已过期" name="expired"
                    :badge-props="expiredCoupons > 0 ? { modelValue: expiredCoupons, right: '-8px' } : null">
                </wd-tab>
            </wd-tabs>
        </view>

        <!-- 券列表 -->
        <view class="coupon-list">
            <view v-if="loading" class="loading-container">
                <wd-loading type="outline" color="#FF9500" />
                <text class="loading-text">加载中...</text>
            </view>

            <view v-else-if="filteredCoupons.length > 0" class="coupon-items">
                <view v-for="(coupon, index) in filteredCoupons" :key="coupon.id" class="coupon-card"
                    :class="getCouponClass(coupon.status)">
                    <!-- 券主体 -->
                    <view class="coupon-main">
                        <!-- 左侧时长信息 -->
                        <view class="coupon-left">
                            <view class="duration-info">
                                <text class="duration-number">{{ coupon.duration }}</text>
                                <text class="duration-unit">小时</text>
                            </view>
                            <view class="coupon-type">时长抵扣券</view>
                        </view>

                        <!-- 中间详情信息 -->
                        <view class="coupon-center">
                            <view class="coupon-title">{{ coupon.title }}</view>
                            <view class="coupon-desc">{{ coupon.description }}</view>
                            <view class="coupon-validity">
                                <text class="ri-time-line validity-icon"></text>
                                <text class="validity-text">有效期至 {{ coupon.expireDate }}</text>
                            </view>
                        </view>

                        <!-- 右侧状态/操作 -->
                        <view class="coupon-right">
                            <view v-if="coupon.status === 'available'" class="coupon-status available">
                                <text class="status-text">去使用</text>
                            </view>
                            <view v-else-if="coupon.status === 'used'" class="coupon-status used">
                                <text class="status-text">已使用</text>
                                <!-- <text class="used-date">{{ coupon.usedDate }}</text> -->
                            </view>
                            <view v-else-if="coupon.status === 'expired'" class="coupon-status expired">
                                <text class="status-text">已过期</text>
                            </view>
                        </view>
                    </view>

                    <!-- 券底部操作 -->
                    <!-- <view v-if="coupon.status === 'available'" class="coupon-actions">
                        <wd-button size="small" type="primary" plain @click="useCoupon(coupon)">
                            立即使用
                        </wd-button>
                        <wd-button size="small" type="info" plain @click="viewCouponDetail(coupon)">
                            查看详情
                        </wd-button>
                    </view> -->
                </view>
            </view>

            <!-- 空状态 -->
            <view v-else class="empty-container">
                <image class="empty-image" src="/static/images/empty-coupon.png" mode="aspectFit"></image>
                <text class="empty-text">{{ getEmptyText() }}</text>
                <wd-button type="primary" size="small" @click="goToRecharge">
                    去充值时长
                </wd-button>
            </view>
        </view>

        <!-- 底部安全区域 -->
        <view class="safe-area-bottom"></view>
    </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 页面数据
const loading = ref(true)
const activeTab = ref('all')

// 模拟券数据
const coupons = ref([
    {
        id: 1,
        title: '新用户专享时长券',
        description: '首次充值赠送，可抵扣任意陪驾时长',
        duration: 2,
        status: 'available',
        expireDate: '2024-03-15',
        createDate: '2024-01-15',
        source: '新用户赠送'
    },
    {
        id: 2,
        title: '充值赠送时长券',
        description: '充值满100小时赠送，限陪驾服务使用',
        duration: 1,
        status: 'available',
        expireDate: '2024-02-28',
        createDate: '2024-01-10',
        source: '充值赠送'
    },
    {
        id: 3,
        title: '活动奖励时长券',
        description: '参与春节活动获得，可抵扣陪驾时长',
        duration: 3,
        status: 'used',
        expireDate: '2024-02-20',
        createDate: '2024-01-05',
        usedDate: '2024-01-18',
        source: '活动奖励'
    },
    {
        id: 4,
        title: '推荐好友时长券',
        description: '成功推荐好友注册获得奖励',
        duration: 1,
        status: 'expired',
        expireDate: '2024-01-20',
        createDate: '2023-12-20',
        source: '推荐奖励'
    },
    {
        id: 5,
        title: '生日专享时长券',
        description: '生日当月专享福利，祝您生日快乐',
        duration: 2,
        status: 'available',
        expireDate: '2024-04-30',
        createDate: '2024-01-20',
        source: '生日福利'
    }
])

// 计算属性
const totalCoupons = computed(() => coupons.value.length)
const availableCoupons = computed(() => coupons.value.filter(c => c.status === 'available').length)
const expiredCoupons = computed(() => coupons.value.filter(c => c.status === 'expired').length)

const filteredCoupons = computed(() => {
    if (activeTab.value === 'all') return coupons.value
    return coupons.value.filter(coupon => coupon.status === activeTab.value)
})

// 方法

const handleTabChange = ({ name }) => {
    activeTab.value = name
}

const getCouponClass = (status) => {
    return `coupon-${status}`
}

const useCoupon = (coupon) => {
    uni.showModal({
        title: '使用时长券',
        content: `确定要使用这张${coupon.duration}小时时长券吗？`,
        success: (res) => {
            if (res.confirm) {
                // 跳转到预约页面
                uni.navigateTo({
                    url: '/pages/index/index'
                })
            }
        }
    })
}

const viewCouponDetail = (coupon) => {
    uni.showModal({
        title: '券详情',
        content: `券名称：${coupon.title}\n时长：${coupon.duration}小时\n来源：${coupon.source}\n有效期：${coupon.expireDate}`,
        showCancel: false
    })
}

const getEmptyText = () => {
    const textMap = {
        all: '暂无时长抵扣券',
        available: '暂无可用的时长券',
        used: '暂无已使用的时长券',
        expired: '暂无过期的时长券'
    }
    return textMap[activeTab.value] || '暂无数据'
}

const goToRecharge = () => {
    uni.navigateTo({
        url: '/pages/recharge/index'
    })
}

// 页面加载
onMounted(() => {
    setTimeout(() => {
        loading.value = false
    }, 1000)
})
</script>

<style>
/* 页面级别样式 */
page {
    background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
    --primary-color: #FF9500;
    --wot-color-theme: #FF9500;
}
</style>

<style lang="scss" scoped>
/* 页面容器 */
.duration-coupon-page {
    min-height: 100vh;
    background: transparent;
}



/* 筛选标签 - 固定在顶部 */
.filter-tabs {
    background: #fff;
    border-bottom: 1rpx solid #f0f0f0;
    position: sticky;
    top: 0;
    z-index: 999;
    padding-top:10rpx;
}

/* 券列表 */
.coupon-list {
    padding: 24rpx 32rpx 120rpx 32rpx;
}

/* 加载状态 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 0;
}

.loading-text {
    font-size: 28rpx;
    color: #999;
    margin-top: 24rpx;
}

/* 券卡片 */
.coupon-card {
    background: #fff;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    position: relative;
}

.coupon-card:active {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

/* 券状态样式 */
.coupon-available {
    // border-left: 6rpx solid #FF9500;
}

.coupon-used {
    // border-left: 6rpx solid #ddd;
    opacity: 0.8;
}

.coupon-expired {
    // border-left: 6rpx solid #ff4757;
    opacity: 0.7;
}

/* 券主体 */
.coupon-main {
    display: flex;
    align-items: center;
    padding: 32rpx;
}

/* 左侧时长信息 */
.coupon-left {
    width: 160rpx;
    text-align: center;
    margin-right: 32rpx;
}

.duration-info {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 12rpx;
}

.duration-number {
    font-size: 52rpx;
    font-weight: 700;
    color: #FF9500;
    font-family: 'DIN Alternate', 'Helvetica Neue', sans-serif;
}

.duration-unit {
    font-size: 22rpx;
    color: #FF9500;
    font-weight: 500;
    margin-left: 4rpx;
}

.coupon-type {
    font-size: 20rpx;
    color: #999;
    background: #f8f9fa;
    padding: 6rpx 12rpx;
    border-radius: 12rpx;
    display: inline-block;
}

/* 中间详情 */
.coupon-center {
    flex: 1;
}

.coupon-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
    line-height: 1.4;
}

.coupon-desc {
    font-size: 24rpx;
    color: #666;
    margin-bottom: 16rpx;
    line-height: 1.4;
}

.coupon-validity {
    display: flex;
    align-items: center;
    gap: 8rpx;
}

.validity-icon {
    font-size: 24rpx;
    color: #999;
}

.validity-text {
    font-size: 22rpx;
    color: #999;
}

/* 右侧状态 */
.coupon-right {
    width: 120rpx;
    text-align: center;
}

.coupon-status {
    padding: 12rpx 16rpx;
    border-radius: 20rpx;
    font-size: 22rpx;
}

.coupon-status.available {
    background: rgba(255, 149, 0, 0.1);
    color: #FF9500;
    font-weight: 600;
}

.coupon-status.used {
    background: #f5f5f5;
    color: #999;
}

.coupon-status.expired {
    background: rgba(255, 71, 87, 0.1);
    color: #ff4757;
}

.used-date {
    font-size: 20rpx;
    color: #ccc;
    margin-top: 8rpx;
    display: block;
}

/* 券操作按钮 */
.coupon-actions {
    padding: 0 32rpx 32rpx 32rpx;
    display: flex;
    gap: 16rpx;
    border-top: 1rpx solid #f5f5f5;
    padding-top: 24rpx;
}

/* 空状态 */
.empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 0;
}

.empty-image {
    width: 240rpx;
    height: 240rpx;
    margin-bottom: 32rpx;
    opacity: 0.6;
}

.empty-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
}

/* 底部安全区域 */
.safe-area-bottom {
    height: env(safe-area-inset-bottom);
    background: transparent;
}

/* 已使用和已过期券的特殊样式 */
.coupon-used .duration-number,
.coupon-used .duration-unit {
    color: #ccc;
}

.coupon-expired .duration-number,
.coupon-expired .duration-unit {
    color: #ff4757;
    opacity: 0.7;
}

.coupon-expired .coupon-title,
.coupon-expired .coupon-desc {
    color: #999;
}

.coupon-used .coupon-title,
.coupon-used .coupon-desc {
    color: #999;
}
</style>