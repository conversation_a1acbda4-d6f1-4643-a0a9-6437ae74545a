
<script setup lang="ts">
	import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'

	// 定义全局数据
	const $globalData = {
		BASE_URL: 'https://api.drive.leapy.cn/user',
		RESOURCE_URL: 'https://resource.leapy.cn/staff/'
	}

	// 使用 uni 对象来存储全局数据
	// @ts-ignore
	uni.$globalData = $globalData

	onLaunch(() => {
		console.log('App Launch')
	})
</script>

<style></style>

<style lang="scss">
	/* 引入remixicon图标库 */
	@import 'static/css/remixicon.css';

	@font-face {
		font-family: SourceHanSansCN;
		/* 思源黑体 */
		src: url('https://resource.leapy.cn/fonts/SourceHanSansCN-Medium.ttf');
	}

	page {
		font-family: SourceHanSansCN;
	}

	.li-single-line {
		white-space: nowrap;
		/* 不换行 */
		overflow: hidden;
		/* 隐藏溢出部分 */
		text-overflow: ellipsis;
		/* 显示省略号 */
	}

	.li-two-line {
		display: -webkit-box;
		/* 使用 Flexbox */
		-webkit-box-orient: vertical;
		/* 垂直排列 */
		-webkit-line-clamp: 2;
		/* 限制显示的行数为 2 */
		overflow: hidden;
		/* 隐藏溢出部分 */
	}

	.li-multi-line {
		display: -webkit-box;
		/* 使用 Flexbox */
		-webkit-box-orient: vertical;
		/* 垂直排列 */
		-webkit-line-clamp: 3;
		/* 限制显示的行数为 3 */
		overflow: hidden;
		/* 隐藏溢出部分 */
	}

	.li-bottom-border {
		border-bottom: solid 1px #F5F7F6;
	}

	.li-bottom-border1 {
		border-bottom: solid 1px #F8F8F8;
	}

	.li-bottom-border2 {
		border-bottom: solid 1px #eaeaea;
	}

	.li-top-border {
		border-top: solid 1px #eaeaea;
	}
</style>