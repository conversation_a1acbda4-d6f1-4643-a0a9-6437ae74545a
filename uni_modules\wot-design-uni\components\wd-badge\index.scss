@import './../common/abstracts/_mixin.scss';
@import './../common/abstracts/variable.scss';

.wot-theme-dark {
  @include b(badge) {
    @include e(content) {
      border-color: $-dark-background2;
    }
  }
}


@include b(badge) {
  position: relative;
  vertical-align: middle;
  display: inline-block;

  @include e(content) {
    display: inline-block;
    height: $-badge-height;
    line-height: $-badge-height;
    padding: $-badge-padding;
    background-color: $-badge-bg;
    border-radius: calc($-badge-height / 2 + 2px);
    color: $-badge-color;
    font-size: $-badge-fs;
    text-align: center;
    white-space: nowrap;
    border: $-badge-border;
    font-weight: 500;

    @include when(fixed) {
      position: absolute;
      top: 0px;
      right: 0px;
      transform: translateY(-50%) translateX(50%);
    }

    @include when(dot) {
      height: $-badge-dot-size;
      width: $-badge-dot-size;
      padding: 0;
      border-radius: 50%;
    }

    @each $type in (primary, success, warning, info, danger) {
      @include m($type) {
        @if $type == primary {
          background-color: $-badge-primary;
        } @else if $type == success {
          background-color: $-badge-success;
        } @else if $type == warning {
          background-color: $-badge-warning;
        } @else if $type == info {
          background-color: $-badge-info;
        } @else {
          background-color: $-badge-danger;
        }
      }
    }
  }
}