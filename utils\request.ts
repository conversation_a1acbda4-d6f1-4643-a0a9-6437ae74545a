import { Uni } from '@dcloudio/uni-app'
import tools from './tools';
import codes from './code.js'
declare const uni : Uni

// 定义请求方法的类型
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE';

interface RequestOptions {
	method ?: HttpMethod;
	data ?: any; // 请求体
}

// 将对象转换为查询字符串的辅助函数
function objectToQueryString(obj : Record<string, any>) : string {
	if (!obj) return '';
	return Object.keys(obj)
		.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`)
		.join('&');
}

// 获取基础URL的函数
function getBaseUrl() {
	// @ts-ignore
	return uni.$globalData?.BASE_URL || 'https://api.drive.leapy.cn/user';
}

// 封装请求函数
export const request = (endpoint : string, options : RequestOptions = {}) => {
	const url = `${endpoint}`;
	return new Promise<any>((resolve, reject) => {
		uni.request({
			url,
			method: options.method || 'GET',
			data: options.data || {},
			header: {
				'Authorization': `Bearer ${tools.storage.get("token")}`,
				'content-type': 'application/json;charset=utf-8'
			},
			success: (response) => {
				if (response.data.code == 0 || response.data.code == 1) {
					resolve(response.data);
				} else if (response.data.code == 3) {
					reject(new Error(`HTTP error! status: ${response.statusCode}`));
					codes.codes()
				}
			},
			fail: (error) => {
				console.error('Network request failed:', error);
				reject(error);
			},
		});
	});
};

// 封装 GET 请求
export const get = (endpoint : string, params ?: any) => {
	const queryString = params ? `?${objectToQueryString(params)}` : '';
	return request(`${getBaseUrl()}${endpoint}${queryString}`, { method: 'GET' });
};

// 封装 POST 请求
export const post = (endpoint : string, data ?: any) => {
	return request(`${getBaseUrl()}${endpoint}`, { method: 'POST', data });
};

// 封装 PUT 请求
export const put = (endpoint : string, data ?: any) => {
	return request(`${getBaseUrl()}${endpoint}`, { method: 'PUT', data });
};

// 封装 DELETE 请求
export const del = (endpoint : string, data ?: any) => {
	return request(`${getBaseUrl()}${endpoint}`, { method: 'DELETE', data });
};