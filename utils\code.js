import {
	login,
} from '@/api/login.ts'
import tools from '@/utils/tools.js'

function codes() {
	// #ifdef MP-WEIXIN
	uni.login({
		success: (res) => {
			console.log(res);
			if (res.errMsg == "login:ok") {
				login({
					code: res.code,
				}).then(res => {
					console.log(res, '---------')
					tools.storage.set('token', res.data.token)
					// 是否已手机号登录授权
					tools.storage.set('needMobile', res.data.needMobile)
					uni.reLaunch({
						url: '/pages/index/index'
					})
				})
			} else {
				uni.showToast({
					title: '系统异常，请联系管理员!'
				})
			}
		}
	})
	// #endif

}


	
// 判断用户师是否已授权
function authorized (){
	var needMobile = tools.storage.get('needMobile') || false
	if(needMobile){
		
	}
	
	
}


export default {
	codes
}