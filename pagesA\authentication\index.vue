<template>
    <view class="auth-container">
        <!-- 平台评价区域 -->
        <view class="rating-section">
            <view class="section-header">
                <text class="ri-heart-3-fill section-icon"></text>
                <text class="section-title">平台评价</text>
                <text class="rating-count">拥有数据好评率</text>
            </view>

            <view class="rating-cards">
                <view class="rating-card good-rating" @click="handleRatingClick">
                    <view class="rating-content">
                        <view class="rating-label">好评率</view>
                        <view class="rating-number">141<text class="rating-unit">条</text></view>
                    </view>
                    <view class="rating-icon">
                        <text class="ri-thumb-up-fill"></text>
                    </view>
                </view>

                <view class="rating-card completion-rating" @click="handleRatingClick">
                    <view class="rating-content">
                        <view class="rating-label">好评率</view>
                        <view class="rating-number">100<text class="rating-unit">%</text></view>
                    </view>
                    <view class="rating-icon">
                        <text class="ri-medal-fill"></text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 身份认证区域 -->
        <view class="cert-section">
            <view class="section-header">
                <text class="ri-user-3-fill section-icon"></text>
                <text class="section-title">身份认证</text>
            </view>

            <view class="cert-item verified">
                <view class="cert-icon">
                    <text class="ri-shield-check-fill"></text>
                </view>
                <view class="cert-info">
                    <view class="cert-title">平台验证通过</view>
                    <view class="cert-desc">实名信息与本人一致</view>
                </view>
                <view class="cert-status">
                    <text class="ri-check-line"></text>
                </view>
            </view>
        </view>

        <!-- 职业认证区域 -->
        <view class="cert-section">
            <view class="section-header">
                <text class="ri-briefcase-fill section-icon"></text>
                <text class="section-title">职业认证</text>
                <text class="cert-count">获取2项认证证书</text>
            </view>

            <view class="cert-grid">
                <view class="cert-card-item">
                    <view class="cert-image">
                        <image src="https://images.unsplash.com/photo-1544620347-c4fd4a3d5957?w=300&h=200&fit=crop"
                            mode="aspectFill"></image>
                        <view class="cert-overlay">
                            <text class="ri-file-text-line"></text>
                        </view>
                    </view>
                    <view class="cert-name">驾驶证</view>
                </view>

                <view class="cert-card-item">
                    <view class="cert-image">
                        <image src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop"
                            mode="aspectFill"></image>
                        <view class="cert-overlay">
                            <text class="ri-award-line"></text>
                        </view>
                    </view>
                    <view class="cert-name">工作证</view>
                </view>
            </view>
        </view>

        <!-- 车辆认证区域 -->
        <view class="cert-section">
            <view class="section-header">
                <text class="ri-car-fill section-icon"></text>
                <text class="section-title">车辆认证</text>
                <text class="cert-count">拥有1辆认证车辆</text>
            </view>

            <view class="vehicle-card">
                <view class="vehicle-info">
                    <view class="vehicle-plate">浙AA61896</view>
                    <view class="vehicle-type">比亚迪秦</view>
                    <view class="vehicle-tag">新能源电车/备车</view>
                </view>
                <view class="vehicle-image">
                    <image src="https://images.unsplash.com/photo-1549317661-bd32c8ce0db2?w=300&h=200&fit=crop"
                        mode="aspectFill"></image>
                </view>
            </view>
        </view>

        <!-- 评价列表区域 -->
        <!-- <view class="reviews-section">
            <view class="section-header">
                <text class="ri-chat-3-fill section-icon"></text>
                <text class="section-title">用户评价</text>
                <text class="review-count">共141条评价</text>
            </view>

            <view class="review-list">
                <view class="review-item" v-for="(review, index) in reviewList" :key="index">
                    <view class="review-header">
                        <view class="user-avatar">
                            <image :src="review.avatar" mode="aspectFill"></image>
                        </view>
                        <view class="user-info">
                            <view class="user-name">{{ review.userName }}</view>
                            <view class="review-date">{{ review.date }}</view>
                        </view>
                        <view class="review-rating">
                            <text class="ri-star-fill" v-for="n in 5" :key="n"
                                :class="{ 'active': n <= review.rating }"></text>
                        </view>
                    </view>
                    <view class="review-content">{{ review.content }}</view>
                    <view class="review-images" v-if="review.images && review.images.length > 0">
                        <image v-for="(img, imgIndex) in review.images" :key="imgIndex" :src="img" mode="aspectFill"
                            class="review-img"></image>
                    </view>
                </view>
            </view>
        </view> -->
    </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'

// 页面参数
const coachId = ref('')
const coachName = ref('')

// 评价数据
const reviewList = ref([
    {
        userName: '张***',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
        date: '2024-07-20',
        rating: 5,
        content: '师傅很专业，教学耐心，车技娴熟，推荐！',
        images: []
    },
    {
        userName: '李***',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
        date: '2024-07-18',
        rating: 5,
        content: '第一次找陪驾，师傅很负责任，讲解很详细，让我对开车更有信心了。',
        images: ['https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=200&h=150&fit=crop']
    },
    {
        userName: '王***',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
        date: '2024-07-15',
        rating: 4,
        content: '整体不错，师傅人很好，就是时间有点紧张。',
        images: []
    },
    {
        userName: '刘***',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        date: '2024-07-12',
        rating: 5,
        content: '非常满意的一次陪驾体验，师傅技术过硬，态度也很好，会推荐给朋友的。',
        images: []
    }
])

// 返回上一页
const goBack = () => {
    uni.navigateBack()
}

// 处理好评率点击
const handleRatingClick = () => {
    uni.navigateTo({
        url: `/pagesA/authentication/evaluation?coachId=${coachId.value}&coachName=${encodeURIComponent(coachName.value)}`
    })
}

// 页面加载
onMounted(() => {
    // 获取页面参数
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    coachId.value = currentPage.options.coachId || ''
    coachName.value = decodeURIComponent(currentPage.options.coachName || '')
})
</script>
<style lang="scss">
page {
    background-color: #f8f9fa;
}
</style>
<style lang="scss" scoped>
.auth-container {
    background: #f8f9fa;
    min-height: 100vh;
}

/* 导航栏 */
.nav-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 32rpx;
    padding-top: calc(20rpx + env(safe-area-inset-top));
    background: #fff;
    border-bottom: 1rpx solid #f0f0f0;
}

.nav-left,
.nav-right {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    color: #333;
}

.nav-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
}

/* 区域样式 */
.rating-section,
.cert-section,
.reviews-section {
    background: #fff;
    margin: 24rpx 32rpx;
    border-radius: 20rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;
}

.section-icon {
    font-size: 32rpx;
    margin-right: 12rpx;
}

.section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    flex: 1;
}

.rating-count,
.cert-count,
.review-count {
    font-size: 24rpx;
    color: #999;
}

/* 评价卡片 */
.rating-cards {
    display: flex;
    gap: 24rpx;
}

.rating-card {
    flex: 1;
    padding: 32rpx 24rpx;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    overflow: hidden;
}

.good-rating {
    background: linear-gradient(135deg, #FFE4B5 0%, #FFF8DC 100%);
}

.completion-rating {
    background: linear-gradient(135deg, #E6E6FA 0%, #F0F8FF 100%);
}

.rating-content {
    z-index: 2;
}

.rating-label {
    font-size: 24rpx;
    color: #666;
    margin-bottom: 8rpx;
}

.rating-number {
    font-size: 48rpx;
    font-weight: 700;
    color: #333;
}

.rating-unit {
    font-size: 24rpx;
    font-weight: 400;
    margin-left: 4rpx;
}

.rating-icon {
    font-size: 48rpx;
    opacity: 0.3;
}

.good-rating .rating-icon {
    color: #FF9500;
}

.completion-rating .rating-icon {
    color: #6366F1;
}

/* 身份认证 */
.cert-item {
    display: flex;
    align-items: center;
    padding: 24rpx;
    background: #f8f9fa;
    border-radius: 16rpx;
    border: 2rpx solid #e9ecef;
}

.cert-item.verified {
    background: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.2);
}

.cert-item .cert-icon {
    width: 80rpx;
    height: 80rpx;
    background: rgba(34, 197, 94, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;
    font-size: 32rpx;
    color: #22c55e;
}

.cert-info {
    flex: 1;
}

.cert-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
}

.cert-desc {
    font-size: 24rpx;
    color: #666;
}

.cert-status {
    font-size: 32rpx;
    color: #22c55e;
}

/* 职业认证网格 */
.cert-grid {
    display: flex;
    gap: 24rpx;
}

.cert-card-item {
    flex: 1;
    text-align: center;
}

.cert-image {
    width: 100%;
    height: 200rpx;
    border-radius: 16rpx;
    overflow: hidden;
    position: relative;
    margin-bottom: 16rpx;
}

.cert-image image {
    width: 100%;
    height: 100%;
}

.cert-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 48rpx;
    color: #fff;
}

.cert-name {
    font-size: 26rpx;
    color: #333;
    font-weight: 500;
}

/* 车辆认证 */
.vehicle-card {
    display: flex;
    align-items: center;
    padding: 24rpx;
    background: #f8f9fa;
    border-radius: 16rpx;
    border: 2rpx solid #e9ecef;
}

.vehicle-info {
    flex: 1;
}

.vehicle-plate {
    font-size: 32rpx;
    font-weight: 700;
    color: #333;
    margin-bottom: 8rpx;
}

.vehicle-type {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 12rpx;
}

.vehicle-tag {
    display: inline-block;
    padding: 6rpx 16rpx;
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
    font-size: 22rpx;
    border-radius: 12rpx;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.vehicle-image {
    width: 120rpx;
    height: 80rpx;
    border-radius: 12rpx;
    overflow: hidden;
    margin-left: 24rpx;
}

.vehicle-image image {
    width: 100%;
    height: 100%;
}

/* 评价列表 */
.review-list {
    display: flex;
    flex-direction: column;
    gap: 32rpx;
}

.review-item {
    padding: 24rpx;
    background: #f8f9fa;
    border-radius: 16rpx;
}

.review-header {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
}

.user-avatar {
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 16rpx;
}

.user-avatar image {
    width: 100%;
    height: 100%;
}

.user-info {
    flex: 1;
}

.user-name {
    font-size: 26rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 4rpx;
}

.review-date {
    font-size: 22rpx;
    color: #999;
}

.review-rating {
    display: flex;
    gap: 4rpx;
}

.review-rating .ri-star-fill {
    font-size: 24rpx;
    color: #ddd;
}

.review-rating .ri-star-fill.active {
    color: #FFD700;
}

.review-content {
    font-size: 26rpx;
    color: #333;
    line-height: 1.6;
    margin-bottom: 16rpx;
}

.review-images {
    display: flex;
    gap: 12rpx;
}

.review-img {
    width: 120rpx;
    height: 120rpx;
    border-radius: 12rpx;
}

/* 区域图标颜色 */
.rating-section .section-icon {
    color: #FF6B6B;
}

.cert-section .section-icon {
    color: #4ECDC4;
}

.reviews-section .section-icon {
    color: #45B7D1;
}
</style>