<template>
	<view class="evaluation-detail-page">
		<!-- 评价信息卡片 -->
		<view class="evaluation-card">
			<view class="evaluation-header">
				<view class="user-info">
					<image class="user-avatar" src="/static/images/1.png" mode="aspectFill"></image>
					<view class="user-details">
						<view class="user-name">我的评价</view>
						<view class="evaluation-date">{{ evaluationData.createTime }}</view>
					</view>
				</view>
				<view class="overall-rating">
					<view class="rating-stars">
						<text class="ri-star-fill" v-for="n in evaluationData.overallRating" :key="n"></text>
						<text class="ri-star-line" v-for="n in (5 - evaluationData.overallRating)" :key="'empty-' + n"></text>
					</view>
					<text class="rating-text">{{ getRatingText(evaluationData.overallRating) }}</text>
				</view>
			</view>

			<!-- 详细评分 -->
			<view class="detail-ratings">
				<view class="rating-item" v-for="(item, index) in evaluationData.detailRatings" :key="index">
					<view class="rating-info">
						<view class="rating-icon">
							<text :class="getRatingIcon(item.category)"></text>
						</view>
						<text class="rating-category">{{ item.category }}</text>
					</view>
					<view class="rating-stars small">
						<text class="ri-star-fill" v-for="n in item.rating" :key="n"></text>
						<text class="ri-star-line" v-for="n in (5 - item.rating)" :key="'empty-' + n"></text>
					</view>
				</view>
			</view>
		</view>

		<!-- 学习内容卡片 -->
		<view class="learning-content-card" v-if="evaluationData.learningContent && evaluationData.learningContent.length > 0">
			<view class="card-header">
				<text class="ri-book-open-line"></text>
				<text class="card-title">我今日学习内容</text>
			</view>
			<view class="learning-items">
				<view class="learning-item" v-for="(item, index) in evaluationData.learningContent" :key="index">
					<text class="item-name">{{ item.name }}</text>
					<text class="item-mastery" :class="getMasteryClass(item.mastery)">{{ item.mastery }}</text>
				</view>
			</view>
		</view>

		<!-- 评价内容卡片 -->
		<view class="content-card">
			<!-- 快捷标签 -->
			<view class="tags-section" v-if="evaluationData.tags && evaluationData.tags.length > 0">
				<view class="section-title">评价标签</view>
				<view class="tags">
					<text class="tag-item" v-for="tag in evaluationData.tags" :key="tag">{{ tag }}</text>
				</view>
			</view>

			<!-- 文字评价 -->
			<view class="text-content" v-if="evaluationData.content">
				<view class="section-title">评价内容</view>
				<text class="content-text">{{ evaluationData.content }}</text>
			</view>

			<!-- 评价图片 -->
			<view class="images-section" v-if="evaluationData.images && evaluationData.images.length > 0">
				<view class="section-title">评价图片</view>
				<view class="images-grid">
					<image 
						v-for="(img, index) in evaluationData.images" 
						:key="index"
						:src="img" 
						mode="aspectFill" 
						class="evaluation-img"
						@click="previewImage(index)"
					></image>
				</view>
			</view>
		</view>

		<!-- 底部安全区域 -->
		<view class="safe-area-bottom"></view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 评价数据
const evaluationData = ref({
	orderId: '',
	createTime: '',
	overallRating: 5,
	detailRatings: [],
	learningContent: [],
	tags: [],
	content: '',
	images: []
})

// 页面加载
onLoad((options) => {
	if (options.orderId) {
		loadEvaluationDetail(options.orderId)
	}
})

// 加载评价详情
const loadEvaluationDetail = async (orderId) => {
	try {
		// 模拟API请求
		await new Promise(resolve => setTimeout(resolve, 500))
		
		// 模拟评价数据
		const mockData = {
			orderId: orderId,
			createTime: '2024-01-16 18:30',
			overallRating: 5,
			detailRatings: [
				{ category: '服务态度', rating: 5 },
				{ category: '教学能力', rating: 5 },
				{ category: '车辆车况', rating: 4 },
				{ category: '准时到达', rating: 5 }
			],
			learningContent: [
				{ name: '普通道路教学', mastery: '熟练掌握' },
				{ name: '练习技巧', mastery: '一般掌握' },
				{ name: '车内功能讲解-初步认识', mastery: '完全掌握' },
				{ name: '交通规则普及', mastery: '熟练掌握' }
			],
			tags: ['态度很好', '很有耐心', '讲解清晰', '技术过硬'],
			content: '张教练非常专业，教学很有耐心，让我学到了很多实用的驾驶技巧。车辆状况良好，整个陪驾过程很愉快。特别是在练习技巧方面，教练给了很多实用的建议，让我的驾驶技术有了明显提升。',
			images: [
				'/static/images/1.png',
				'/static/images/2.png'
			]
		}
		
		evaluationData.value = mockData
		console.log('加载评价详情:', orderId)
	} catch (error) {
		console.error('加载评价详情失败:', error)
		uni.showToast({
			title: '加载失败',
			icon: 'error'
		})
	}
}

// 获取评分文字描述
const getRatingText = (rating) => {
	const texts = ['', '很差', '较差', '一般', '满意', '非常满意']
	return texts[rating] || ''
}

// 获取评分图标
const getRatingIcon = (category) => {
	const iconMap = {
		'服务态度': 'ri-heart-line',
		'教学能力': 'ri-book-open-line',
		'车辆车况': 'ri-car-line',
		'准时到达': 'ri-time-line'
	}
	return iconMap[category] || 'ri-star-line'
}

// 获取掌握程度样式类
const getMasteryClass = (mastery) => {
	const classMap = {
		'一般掌握': 'mastery-basic',
		'熟练掌握': 'mastery-skilled',
		'完全掌握': 'mastery-expert'
	}
	return classMap[mastery] || 'mastery-basic'
}

// 预览图片
const previewImage = (index) => {
	uni.previewImage({
		urls: evaluationData.value.images,
		current: index
	})
}
</script>

<style lang="scss">
/* 页面级别样式 */
page {
	background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
	height: 100%;
	--primary-color: #FF9500;
}
</style>

<style lang="scss" scoped>
.evaluation-detail-page {
	min-height: 100vh;
	padding: 32rpx;
	padding-bottom: 120rpx;
}

/* 评价信息卡片 */
.evaluation-card {
	background: linear-gradient(135deg, 
		rgba(255, 248, 240, 0.95) 0%, 
		rgba(255, 251, 245, 0.95) 50%, 
		rgba(248, 250, 255, 0.95) 100%);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 24rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
	box-shadow: 
		0 8rpx 32rpx rgba(0, 0, 0, 0.08),
		0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.evaluation-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 32rpx;
	padding-bottom: 24rpx;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
}

.user-info {
	display: flex;
	align-items: center;
}

.user-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-right: 20rpx;
	border: 3rpx solid #fff;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.user-details {
	display: flex;
	flex-direction: column;
}

.user-name {
	font-size: 28rpx;
	font-weight: 700;
	color: #1a1a1a;
	margin-bottom: 6rpx;
}

.evaluation-date {
	font-size: 22rpx;
	color: #999;
}

.overall-rating {
	text-align: right;
}

.rating-stars {
	display: flex;
	gap: 4rpx;
	margin-bottom: 8rpx;
	justify-content: flex-end;
}

.rating-stars .ri-star-fill {
	font-size: 28rpx;
	color: #ffc107;
}

.rating-stars .ri-star-line {
	font-size: 28rpx;
	color: #ddd;
}

.rating-stars.small .ri-star-fill,
.rating-stars.small .ri-star-line {
	font-size: 24rpx;
}

.rating-text {
	font-size: 22rpx;
	color: #FF9500;
	font-weight: 600;
}

/* 详细评分 */
.detail-ratings {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.rating-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	background: rgba(255, 255, 255, 0.6);
	border-radius: 16rpx;
	border: 1rpx solid rgba(0, 0, 0, 0.04);
}

.rating-info {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.rating-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #64748b 0%, #475569 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 16rpx rgba(100, 116, 139, 0.2);
	
	.ri-heart-line,
	.ri-book-open-line,
	.ri-car-line,
	.ri-time-line {
		font-size: 18rpx;
		color: #fff;
	}
}

.rating-category {
	font-size: 26rpx;
	font-weight: 600;
	color: #1a1a1a;
}

/* 学习内容卡片 */
.learning-content-card, .content-card {
	background: linear-gradient(135deg,
		rgba(255, 255, 255, 0.95) 0%,
		rgba(248, 250, 255, 0.95) 100%);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 24rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
	box-shadow:
		0 8rpx 32rpx rgba(0, 0, 0, 0.08),
		0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.card-header {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-bottom: 24rpx;
	padding: 16rpx 20rpx;
	background: rgba(255, 149, 0, 0.05);
	border-radius: 16rpx;
	border-left: 4rpx solid #64748b;
}

.card-header .ri-book-open-line {
	font-size: 24rpx;
	color: #64748b;
}

.card-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #1a1a1a;
}

.learning-items {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.learning-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	background: rgba(255, 255, 255, 0.8);
	border-radius: 16rpx;
	border: 1rpx solid rgba(0, 0, 0, 0.04);
}

.item-name {
	font-size: 28rpx;
	font-weight: 600;
	color: #1a1a1a;
}

.item-mastery {
	font-size: 24rpx;
	font-weight: 600;
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
}

.item-mastery.mastery-basic {
	background: rgba(156, 163, 175, 0.1);
	color: #6b7280;
}

.item-mastery.mastery-skilled {
	background: rgba(255, 149, 0, 0.1);
	color: #FF9500;
}

.item-mastery.mastery-expert {
	background: rgba(34, 197, 94, 0.1);
	color: #22c55e;
}

/* 评价内容区域 */
.section-title {
	font-size: 28rpx;
	font-weight: 700;
	color: #1a1a1a;
	margin-bottom: 20rpx;
}

.tags-section, .text-content, .images-section {
	margin-bottom: 32rpx;
}

.tags-section:last-child, .text-content:last-child, .images-section:last-child {
	margin-bottom: 0;
}

.tags {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.tag-item {
	padding: 12rpx 24rpx;
	background: rgba(255, 149, 0, 0.1);
	border: 2rpx solid #FF9500;
	border-radius: 24rpx;
	font-size: 26rpx;
	color: #FF9500;
	font-weight: 600;
}

.content-text {
	font-size: 28rpx;
	color: #1a1a1a;
	line-height: 1.8;
	text-align: justify;
}

.images-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.evaluation-img {
	width: 200rpx;
	height: 200rpx;
	border-radius: 12rpx;
	border: 2rpx solid rgba(0, 0, 0, 0.08);
}

.evaluation-img:active {
	transform: scale(0.95);
}

/* 底部安全区域 */
.safe-area-bottom {
	height: env(safe-area-inset-bottom);
}
</style>
