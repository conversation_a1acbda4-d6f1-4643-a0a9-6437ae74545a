// 课程类型定义
export interface Course {
  id: number;
  title: string;
  people: string;
  price: string;
  originalPrice: string;
  image: string;
  category?: string;
  isPopular?: boolean;
  isNew?: boolean;
}

// 定义uni对象，避免TypeScript报错
declare global {
  const uni: {
    navigateTo(options: { url: string }): void;
    showToast(options: { title: string; icon?: string }): void;
    reLaunch(options: { url: string }): void;
    navigateBack(options?: { delta?: number }): void;
    makePhoneCall(options: { phoneNumber: string }): void;
    showModal(options: { 
      title: string; 
      content: string; 
      success?: (res: { confirm: boolean; cancel: boolean }) => void 
    }): void;
    getSystemInfo(options: {
      success?: (res: {
        statusBarHeight?: number;
        [key: string]: any;
      }) => void;
      fail?: (err: any) => void;
      complete?: () => void;
    }): void;
    $globalData?: {
      BASE_URL: string;
      RESOURCE_URL: string;
    };
  };
} 