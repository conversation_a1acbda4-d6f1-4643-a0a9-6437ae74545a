<template>
    <view class="evaluate-page">
        <!-- 订单信息卡片 -->
        <view class="order-info-card">
            <view class="coach-section">
                <image class="coach-avatar" :src="orderInfo.coachAvatar || defaultAvatar" mode="aspectFill"></image>
                <view class="coach-info">
                    <view class="coach-name">{{ orderInfo.coachName }}</view>
                    <view class="order-details">
                        <text class="detail-text">{{ orderInfo.appointmentTime }}</text>
                        <text class="detail-text">{{ orderInfo.carType }}</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 评分区域 -->
        <view class="rating-section">
            <view class="section-title">服务评价</view>

            <!-- 总体评分 -->
            <view class="overall-rating">
                <text class="rating-label">总体评分</text>
                <view class="star-rating">
                    <text v-for="(star, index) in 5" :key="index" class="star-item"
                        :class="{ 'active': index < overallRating }" @click="setOverallRating(index + 1)">
                        <text class="ri-star-fill" v-if="index < overallRating"></text>
                        <text class="ri-star-line" v-else></text>
                    </text>
                </view>
                <text class="rating-text">{{ getRatingText(overallRating) }}</text>
            </view>

            <!-- 详细评分 -->
            <view class="detailed-ratings">
                <view class="rating-item" v-for="(item, index) in ratingItems" :key="index">
                    <view class="rating-info">
                        <view class="rating-icon">
                            <text :class="item.icon"></text>
                        </view>
                        <text class="rating-category">{{ item.name }}</text>
                    </view>
                    <view class="star-rating small">
                        <text v-for="(star, starIndex) in 5" :key="starIndex" class="star-item"
                            :class="{ 'active': starIndex < item.rating }"
                            @click="setDetailRating(index, starIndex + 1)">
                            <text class="ri-star-fill" v-if="starIndex < item.rating"></text>
                            <text class="ri-star-line" v-else></text>
                        </text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 学习内容 -->
        <view class="content-section">
            <view class="section-title">学习内容</view>

            <!-- 学习内容选择 -->
            <view class="learning-content-section">
                <view class="content-header">
                    <text class="ri-book-open-line"></text>
                    <text class="content-title">我今日学习内容</text>
                </view>
                <view class="learning-items">
                    <view class="learning-item" v-for="(item, index) in learningItems" :key="index"
                        :class="{ 'selected': item.selected }" @click="toggleLearningItem(index)">
                        <view class="item-info">
                            <text class="item-name">{{ item.name }}</text>
                            <view class="mastery-level" v-if="item.selected">
                                <text class="level-option" v-for="level in masteryLevels" :key="level"
                                    :class="{ 'active': item.mastery === level }"
                                    @click.stop="setMasteryLevel(index, level)">
                                    {{ level }}
                                </text>
                            </view>
                        </view>
                        <view class="item-status">
                            <text class="status-text" v-if="item.selected">{{ item.mastery || '请选择' }}</text>
                            <text class="ri-add-line" v-else></text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 评价内容 -->
        <view class="content-section">
            <view class="section-title">评价内容</view>

            <!-- 快捷标签 -->
            <view class="quick-tags">
                <view class="tag-group">
                    <text class="tag-group-title">服务态度</text>
                    <view class="tags">
                        <text v-for="tag in serviceTags" :key="tag" class="tag-item"
                            :class="{ 'active': selectedTags.includes(tag) }" @click="toggleTag(tag)">
                            {{ tag }}
                        </text>
                    </view>
                </view>
                <view class="tag-group">
                    <text class="tag-group-title">教学质量</text>
                    <view class="tags">
                        <text v-for="tag in teachingTags" :key="tag" class="tag-item"
                            :class="{ 'active': selectedTags.includes(tag) }" @click="toggleTag(tag)">
                            {{ tag }}
                        </text>
                    </view>
                </view>
            </view>

            <!-- 文字评价 -->
            <view class="text-input-section">
                <textarea class="evaluation-textarea" v-model="evaluationText"
                    placeholder="请详细描述您的陪驾体验，您的评价对其他学员很有帮助..." placeholder-style="font-size: 28rpx; color: #999;" :maxlength="500" auto-height></textarea>
                <view class="char-count">{{ evaluationText.length }}/500</view>
            </view>

            <!-- 图片上传 -->
            <view class="image-upload-section">
                <view class="upload-title">上传图片（可选）</view>
                <view class="image-list">
                    <view class="image-item" v-for="(img, index) in uploadedImages" :key="index">
                        <image :src="img" mode="aspectFill" class="uploaded-img"></image>
                        <view class="delete-btn" @click="removeImage(index)">
                            <text class="ri-close-line"></text>
                        </view>
                    </view>
                    <view class="upload-btn" @click="chooseImage" v-if="uploadedImages.length < 6">
                        <text class="ri-camera-line"></text>
                        <text class="upload-text">添加图片</text>
                    </view>
                </view>
                <view class="upload-tip">最多可上传6张图片</view>
            </view>
        </view>

        <!-- 底部提交按钮 -->
        <view class="submit-section">
            <button class="submit-btn" @click="submitEvaluation" :disabled="!canSubmit">
                提交评价
            </button>
        </view>

        <!-- 底部安全区域 -->
        <view class="safe-area-bottom"></view>
    </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 默认头像
const defaultAvatar = '/static/images/1.png'

// 订单信息
const orderInfo = ref({
    id: '',
    coachName: '',
    coachAvatar: '',
    appointmentTime: '',
    carType: ''
})

// 评分数据
const overallRating = ref(5) // 总体评分
const ratingItems = ref([
    { name: '服务态度', icon: 'ri-heart-line', rating: 5 },
    { name: '教学能力', icon: 'ri-book-open-line', rating: 5 },
    { name: '车辆车况', icon: 'ri-car-line', rating: 5 },
    { name: '准时到达', icon: 'ri-time-line', rating: 5 }
])

// 学习内容
const learningItems = ref([
    { name: '普通道路教学', selected: false, mastery: '' },
    { name: '适应你客户制', selected: false, mastery: '' },
    { name: '练习技巧', selected: false, mastery: '' },
    { name: '车内功能讲解-初步认识', selected: false, mastery: '' },
    { name: '交通规则普及', selected: false, mastery: '' },
    { name: '安全训练', selected: false, mastery: '' },
    { name: '紧急停车练', selected: false, mastery: '' },
    { name: '转弯练习', selected: false, mastery: '' },
    { name: '跟车练习', selected: false, mastery: '' },
    { name: '网方记忆车身练习', selected: false, mastery: '' }
])

const masteryLevels = ref(['一般掌握', '熟练掌握', '完全掌握'])

// 快捷标签
const serviceTags = ref(['态度很好', '很有耐心', '专业负责', '服务周到', '热情友善'])
const teachingTags = ref(['讲解清晰', '技术过硬', '经验丰富', '因材施教', '细致认真'])
const selectedTags = ref([])

// 评价内容
const evaluationText = ref('')
const uploadedImages = ref([])

// 页面加载
onLoad((options) => {
    if (options.orderId) {
        loadOrderInfo(options.orderId)
    }
})

// 加载订单信息
const loadOrderInfo = async (orderId) => {
    try {
        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 300))

        // 模拟数据
        const mockData = {
            id: orderId,
            coachName: '张教练',
            coachAvatar: '/static/images/2.png',
            appointmentTime: '2024-01-16 14:00',
            carType: '大众朗逸'
        }

        orderInfo.value = mockData
        console.log('加载订单信息:', orderId)
    } catch (error) {
        console.error('加载订单信息失败:', error)
        uni.showToast({
            title: '加载失败',
            icon: 'error'
        })
    }
}

// 设置总体评分
const setOverallRating = (rating) => {
    overallRating.value = rating
    // 同时更新所有详细评分
    ratingItems.value.forEach(item => {
        item.rating = rating
    })
}

// 设置详细评分
const setDetailRating = (index, rating) => {
    ratingItems.value[index].rating = rating
    // 更新总体评分为平均值
    const average = ratingItems.value.reduce((sum, item) => sum + item.rating, 0) / ratingItems.value.length
    overallRating.value = Math.round(average)
}

// 获取评分文字描述
const getRatingText = (rating) => {
    const texts = ['', '很差', '较差', '一般', '满意', '非常满意']
    return texts[rating] || ''
}

// 切换学习内容选择
const toggleLearningItem = (index) => {
    learningItems.value[index].selected = !learningItems.value[index].selected
    if (!learningItems.value[index].selected) {
        learningItems.value[index].mastery = ''
    }
}

// 设置掌握程度
const setMasteryLevel = (index, level) => {
    learningItems.value[index].mastery = level
}

// 切换标签选择
const toggleTag = (tag) => {
    const index = selectedTags.value.indexOf(tag)
    if (index > -1) {
        selectedTags.value.splice(index, 1)
    } else {
        selectedTags.value.push(tag)
    }
}

// 选择图片
const chooseImage = () => {
    uni.chooseImage({
        count: 6 - uploadedImages.value.length,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
            uploadedImages.value.push(...res.tempFilePaths)
        },
        fail: () => {
            uni.showToast({
                title: '选择图片失败',
                icon: 'none'
            })
        }
    })
}

// 删除图片
const removeImage = (index) => {
    uploadedImages.value.splice(index, 1)
}

// 是否可以提交
const canSubmit = computed(() => {
    return overallRating.value > 0 && (evaluationText.value.trim().length > 0 || selectedTags.value.length > 0)
})

// 提交评价
const submitEvaluation = async () => {
    if (!canSubmit.value) {
        uni.showToast({
            title: '请完善评价内容',
            icon: 'none'
        })
        return
    }

    try {
        uni.showLoading({
            title: '提交中...'
        })

        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 1500))

        // 构建评价数据
        const evaluationData = {
            orderId: orderInfo.value.id,
            overallRating: overallRating.value,
            detailRatings: ratingItems.value.map(item => ({
                category: item.name,
                rating: item.rating
            })),
            learningContent: learningItems.value
                .filter(item => item.selected && item.mastery)
                .map(item => ({
                    name: item.name,
                    mastery: item.mastery
                })),
            tags: selectedTags.value,
            content: evaluationText.value.trim(),
            images: uploadedImages.value
        }

        console.log('提交评价数据:', evaluationData)

        // 这里应该调用实际的API
        // const response = await uni.request({
        //     url: '/api/orders/evaluate',
        //     method: 'POST',
        //     data: evaluationData
        // })

        uni.hideLoading()

        uni.showToast({
            title: '评价提交成功',
            icon: 'success'
        })

        // 延迟返回上一页
        setTimeout(() => {
            uni.navigateBack()
        }, 1500)

    } catch (error) {
        uni.hideLoading()
        console.error('提交评价失败:', error)
        uni.showToast({
            title: '提交失败，请重试',
            icon: 'error'
        })
    }
}
</script>

<style lang="scss">
/* 页面级别样式 */
page {
    background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
    height: 100%;
    --primary-color: #FF9500;
}
</style>

<style lang="scss" scoped>
.evaluate-page {
    min-height: 100vh;
    padding: 32rpx;
    padding-bottom: 200rpx;
}

/* 订单信息卡片 */
.order-info-card {
    background: linear-gradient(135deg,
            rgba(255, 248, 240, 0.95) 0%,
            rgba(255, 251, 245, 0.95) 50%,
            rgba(248, 250, 255, 0.95) 100%);
    backdrop-filter: blur(20rpx);
    -webkit-backdrop-filter: blur(20rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    border-radius: 24rpx;
    padding: 32rpx;
    margin-bottom: 32rpx;
    box-shadow:
        0 8rpx 32rpx rgba(0, 0, 0, 0.08),
        0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.coach-section {
    display: flex;
    align-items: center;
}

.coach-avatar {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    margin-right: 24rpx;
    flex-shrink: 0;
    border: 3rpx solid #fff;
    box-shadow:
        0 4rpx 16rpx rgba(0, 0, 0, 0.1),
        0 0 0 1rpx rgba(255, 149, 0, 0.1);
}

.coach-info {
    flex: 1;
}

.coach-name {
    font-size: 32rpx;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 8rpx;
}

.order-details {
    display: flex;
    flex-direction: column;
    gap: 4rpx;
}

.detail-text {
    font-size: 26rpx;
    color: #666;
}

/* 评分区域 */
.rating-section,
.content-section {
    background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(248, 250, 255, 0.95) 100%);
    backdrop-filter: blur(20rpx);
    -webkit-backdrop-filter: blur(20rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    border-radius: 24rpx;
    padding: 32rpx;
    margin-bottom: 32rpx;
    box-shadow:
        0 8rpx 32rpx rgba(0, 0, 0, 0.08),
        0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.section-title {
    font-size: 32rpx;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 32rpx;
}

/* 总体评分 */
.overall-rating {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx;
    background: rgba(255, 149, 0, 0.05);
    border-radius: 20rpx;
    margin-bottom: 32rpx;
}

.rating-label {
    font-size: 28rpx;
    font-weight: 600;
    color: #1a1a1a;
}

.star-rating {
    display: flex;
    gap: 8rpx;
}

.star-rating.small {
    gap: 4rpx;
}

.star-item {
    font-size: 40rpx;
    color: #ddd;
    transition: all 0.2s ease;
}

.star-rating.small .star-item {
    font-size: 32rpx;
}

.star-item.active {
    color: #ffc107;
}

.star-item:active {
    transform: scale(1.1);
}

.rating-text {
    font-size: 26rpx;
    color: #FF9500;
    font-weight: 600;
}

/* 详细评分 */
.detailed-ratings {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
}

.rating-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 16rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.04);
}

.rating-info {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.rating-icon {
    width: 48rpx;
    height: 48rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 16rpx rgba(100, 116, 139, 0.2);

    .ri-heart-line,
    .ri-book-open-line,
    .ri-car-line,
    .ri-time-line {
        font-size: 20rpx;
        color: #fff;
    }
}

.rating-category {
    font-size: 28rpx;
    font-weight: 600;
    color: #1a1a1a;
}

/* 学习内容区域 */
.learning-content-section {
    margin-bottom: 32rpx;
}

.content-header {
    display: flex;
    align-items: center;
    gap: 12rpx;
    margin-bottom: 24rpx;
    padding: 16rpx 20rpx;
    background: rgba(255, 149, 0, 0.05);
    border-radius: 16rpx;
    border-left: 4rpx solid #64748b;
}

.content-header .ri-book-open-line {
    font-size: 24rpx;
    color: #64748b;
}

.content-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #1a1a1a;
}

.learning-items {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
}

.learning-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx;
    background: rgba(255, 255, 255, 0.8);
    border: 2rpx solid rgba(0, 0, 0, 0.08);
    border-radius: 16rpx;
    transition: all 0.3s ease;
}

.learning-item.selected {
    background: rgba(255, 149, 0, 0.05);
    border-color: #FF9500;
}

.learning-item:active {
    transform: scale(0.98);
}

.item-info {
    flex: 1;
}

.item-name {
    font-size: 28rpx;
    font-weight: 600;
    color: #1a1a1a;
    display: block;
    margin-bottom: 8rpx;
}

.mastery-level {
    display: flex;
    gap: 12rpx;
    margin-top: 12rpx;
}

.level-option {
    padding: 6rpx 16rpx;
    background: rgba(255, 255, 255, 0.8);
    border: 1rpx solid rgba(0, 0, 0, 0.1);
    border-radius: 20rpx;
    font-size: 22rpx;
    color: #666;
    transition: all 0.3s ease;
}

.level-option.active {
    background: #FF9500;
    color: #fff;
    border-color: #FF9500;
}

.level-option:active {
    transform: scale(0.95);
}

.item-status {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 120rpx;
}

.status-text {
    font-size: 24rpx;
    color: #FF9500;
    font-weight: 600;
    padding: 8rpx 16rpx;
    background: rgba(255, 149, 0, 0.1);
    border-radius: 12rpx;
}

.ri-add-line {
    font-size: 32rpx;
    color: #ccc;
    width: 40rpx;
    height: 40rpx;
    border: 2rpx dashed #ccc;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 快捷标签 */
.quick-tags {
    margin-bottom: 32rpx;
}

.tag-group {
    margin-bottom: 24rpx;
}

.tag-group:last-child {
    margin-bottom: 0;
}

.tag-group-title {
    font-size: 26rpx;
    font-weight: 600;
    color: #666;
    margin-bottom: 16rpx;
    display: block;
}

.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
}

.tag-item {
    padding: 12rpx 24rpx;
    background: rgba(255, 255, 255, 0.8);
    border: 2rpx solid rgba(0, 0, 0, 0.08);
    border-radius: 24rpx;
    font-size: 26rpx;
    color: #666;
    transition: all 0.3s ease;
}

.tag-item.active {
    background: rgba(255, 149, 0, 0.1);
    border-color: #FF9500;
    color: #FF9500;
    font-weight: 600;
}

.tag-item:active {
    transform: scale(0.95);
}

/* 文字输入区域 */
.text-input-section {
    margin-bottom: 32rpx;
    position: relative;
    width: 100%;
    margin: 0 auto;

}

.evaluation-textarea {
    width:100%;
    margin: 0 auto;
    min-height: 230rpx;
    padding: 24rpx 20rpx; 
    background: rgba(255, 255, 255, 0.8);
    border: 2rpx solid rgba(0, 0, 0, 0.08);
    border-radius: 16rpx;
    font-size: 28rpx;
    color: #1a1a1a;
    line-height: 1.6;
    resize: none;
    box-sizing: border-box;
    display: block;
}

.evaluation-textarea:focus {
    border-color: #FF9500;
    background: rgba(255, 255, 255, 0.95);
}

.char-count {
    position: absolute;
    bottom: 16rpx;
    right: 10%;
    transform: translateX(50%);
    font-size: 22rpx;
    color: #999;
    text-align: center;
}

/* 图片上传区域 */
.image-upload-section {
    margin: 32rpx 0;
}

.upload-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 16rpx;
}

.image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
}

.image-item {
    position: relative;
    width: 160rpx;
    height: 160rpx;
}

.uploaded-img {
    width: 100%;
    height: 100%;
    border-radius: 12rpx;
    border: 2rpx solid rgba(0, 0, 0, 0.08);
}

.delete-btn {
    position: absolute;
    top: -8rpx;
    right: -8rpx;
    width: 32rpx;
    height: 32rpx;
    background: #ff4757;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);

    .ri-close-line {
        font-size: 20rpx;
        color: #fff;
    }
}

.upload-btn {
    width: 160rpx;
    height: 160rpx;
    border: 2rpx dashed rgba(255, 149, 0, 0.3);
    border-radius: 12rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 149, 0, 0.05);
    transition: all 0.3s ease;

    .ri-camera-line {
        font-size: 40rpx;
        color: #FF9500;
        margin-bottom: 8rpx;
    }
}

.upload-text {
    font-size: 22rpx;
    color: #FF9500;
}

.upload-btn:active {
    background: rgba(255, 149, 0, 0.1);
    transform: scale(0.95);
}

.upload-tip {
    font-size: 22rpx;
    color: #999;
    margin-top: 16rpx;
    text-align: center;
}

/* 提交按钮区域 */
.submit-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(248, 250, 255, 0.95) 100%);
    backdrop-filter: blur(20rpx);
    -webkit-backdrop-filter: blur(20rpx);
    border-top: 1rpx solid rgba(255, 255, 255, 0.3);
    padding: 32rpx;
    padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
    box-shadow: 0 -4rpx 32rpx rgba(0, 0, 0, 0.08);
}

.submit-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #FF9500 0%, #ff7b00 100%);
    color: #fff;
    border: none;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 24rpx rgba(255, 149, 0, 0.3);
    transition: all 0.3s ease;
}

.submit-btn:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.4);
}

.submit-btn:disabled {
    background: #ccc;
    box-shadow: none;
    transform: none;
}

/* 底部安全区域 */
.safe-area-bottom {
    height: env(safe-area-inset-bottom);
}
</style>