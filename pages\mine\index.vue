<template>
	<view class="mine-container">
		<!-- 顶部状态栏 -->
		<view class="status-bar">
			<!-- 状态栏占位，自动适配不同机型 -->
			<view class="status-bar-height"></view>
		</view>

		<!-- 胶囊按钮占位 -->
		<view class="capsule-placeholder" :style="capsuleStyle"></view>

		<!-- 用户信息区域 -->
		<view class="user-info-section" @click="handleUserInfoClick">
			<view class="user-card">
				<image class="avatar" :src="userInfo.avatar || '/static/images/default-avatar.png'" mode="aspectFill">
				</image>
				<view class="user-detail">
					<view class="username">{{ userInfo.nickname || '路路安客服' }}</view>
					<view class="user-location">
						<text class="ri-loader-line location-icon"></text>
						<text class="location-text">17729744485</text>
					</view>
				</view>
				<view class="profile-arrow">
					<text class="ri-arrow-right-s-line arrow-icon"></text>
				</view>
			</view>
		</view>

		<!-- 剩余时长卡片 -->
		<view class="time-card">
			<view class="time-card-bg"></view>
			<view class="time-content">
				<view class="time-info">
					<view class="time-title">剩余陪驾时长</view>
					<view class="time-value">{{ userInfo.remainingTime || '0' }} <text class="time-unit">小时</text>
					</view>
				</view>
				<view class="time-button" @click="handleRecharge">
					<text>充值时长</text>
				</view>
			</view>
			<view class="time-warning" v-if="userInfo.remainingTime < 3">
				<text class="ri-alert-line warning-icon"></text>
				<text class="warning-text">您的剩余时长不足3小时，建议及时充值</text>
			</view>
		</view>

		<!-- 我的订单 -->
		<view class="order-section">
			<view class="section-header">
				<view class="section-title">我的陪驾订单</view>
				<view class="view-all" @click="navigateTo('/pagesA/orders/index')">
					查看全部 <text class="ri-arrow-right-s-line"></text>
				</view>
			</view>

			<view class="order-status-tabs">
				<view v-for="(item, index) in orderStatusList" :key="index" class="status-item"
					@click="changeOrderStatus(item.status)">
					<wd-badge :modelValue="item.count" max="99">
						<image class="status-image" :src="item.icon" mode="aspectFit"></image>
					</wd-badge>
					<text class="status-text">{{ item.name }}</text>
				</view>
			</view>
		</view>

		<!-- 服务菜单 -->
		<view class="service-section">
			<view class="section-title">我的服务</view>
			<view class="service-list">
				<view class="service-item" @click="navigateTo('/pagesA/coupon/duration')">
					<view class="service-item-left">
						<view class="cell-icon">
							<text class="ri-coupon-3-line li-text-40"></text>
						</view>
						<text class="service-item-title">时长卡抵扣券</text>
					</view>
					<view class="service-item-right">
						<text class="service-item-value">3张</text>
						<text class="ri-arrow-right-s-line"></text>
					</view>
				</view>
				
				<view class="service-item" @click="navigateTo('/pagesA/coupon/cash')">
					<view class="service-item-left">
						<view class="cell-icon">
							<text class="ri-money-cny-box-line li-text-40"></text>
						</view>
						<text class="service-item-title">现金抵用券</text>
					</view>
					<view class="service-item-right">
						<text class="service-item-value">2张</text>
						<text class="ri-arrow-right-s-line"></text>
					</view>
				</view>
				
				<view class="service-item" @click="handleContact">
					<view class="service-item-left">
						<view class="cell-icon">
							<text class="ri-customer-service-2-line li-text-40"></text>
						</view>
						<text class="service-item-title">联系我们</text>
					</view>
					<view class="service-item-right">
						<text class="ri-arrow-right-s-line"></text>
					</view>
				</view>
				
				<view class="service-item" @click="navigateTo('/pages/agreement/index')">
					<view class="service-item-left">
						<view class="cell-icon">
							<text class="ri-file-text-line li-text-40"></text>
						</view>
						<text class="service-item-title">陪驾协议</text>
					</view>
					<view class="service-item-right">
						<text class="ri-arrow-right-s-line"></text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 用户信息
const userInfo = ref({
	avatar: '/static/images/2.png', // 默认头像，实际使用时应该从用户数据中获取
	nickname: '路路安客服', // 用户昵称
	location: '17729744485', // 用户电话
	isLoggedIn: true, // 是否已登录
	remainingTime: 2.5, // 剩余陪驾时长(小时)
});

// 订单状态列表
const orderStatusList = ref([
	{ name: '全部', status: 'all', icon: '/static/mine/quanbu.png', count: 3 },
	{ name: '待进行', status: 'pending', icon: '/static/mine/daijinxing.png', count: 0 },
	{ name: '待确认', status: 'confirming', icon: '/static/mine/daiqueren.png', count: 0 },
	{ name: '待评价', status: 'rating', icon: '/static/mine/daipingjia.png', count: 3 }
]);

// 当前激活的订单状态
const activeOrderStatus = ref('all');

// 切换订单状态
const changeOrderStatus = (status) => {
	activeOrderStatus.value = status;
	// 跳转到订单列表页面，传递状态参数
	uni.navigateTo({
		url: `/pagesA/orders/index?status=${status}`
	});
};

// 页面导航
const navigateTo = (url) => {
	uni.navigateTo({ url });
};

// 服务项目
const serviceItems = ref([
	{ icon: 'ri-coupon-3-line', name: '兑换中心' },
	{ icon: 'ri-message-3-line', name: '帮助与反馈' },
	{ icon: 'ri-heart-3-line', name: '好评鼓励' },
	{ icon: 'ri-information-line', name: '关于我们' },
	{ icon: 'ri-cloud-line', name: '气象服务' },
]);

// 胶囊按钮信息
const capsuleStyle = ref({});

// 获取胶囊按钮信息
onMounted(() => {
	// #ifdef MP-WEIXIN
	try {
		const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
		const systemInfo = uni.getSystemInfoSync();

		// 计算胶囊按钮高度和边距
		const capsuleHeight = menuButtonInfo.height;
		const capsuleMargin = (menuButtonInfo.top - systemInfo.statusBarHeight) * 2;

		// 设置样式变量
		capsuleStyle.value = {
			height: capsuleHeight + capsuleMargin + 'px'
		};
	} catch (e) {
		console.error('获取胶囊按钮信息失败', e);
	}
	// #endif
});

// 处理充值时长
const handleRecharge = () => {
	uni.switchTab({
		url: '/pages/menu/index'
	});
};

const handleUserInfoClick = () => {
	uni.navigateTo({
		url: '/pagesA/message/index'
	});
};

// 处理联系我们
const handleContact = () => {
	uni.makePhoneCall({
		phoneNumber: '17729744485',
		success: () => {
			console.log('拨打电话成功');
		},
		fail: (err) => {
			console.error('拨打电话失败', err);
		}
	});
};
</script>

<style lang="scss">
page {
	background-color: #f8f8f8;
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
	height: 100%;
}

.mine-container {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	padding: 0 30rpx;
}

/* 顶部状态栏 */
.status-bar {
	width: 100%;
	position: relative;
}

.status-bar-height {
	height: var(--status-bar-height);
	width: 100%;
}

/* 胶囊按钮占位 */
.capsule-placeholder {
	height: 44px;
	/* 默认高度 */
	width: 100%;
}

/* 用户信息区域 */
.user-info-section {
	margin-top: 20rpx;
}

.user-card {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
}

.avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	border: 4rpx solid #f0f0f0;
}

.user-detail {
	flex: 1;
	padding-left: 20rpx;
}

.username {
	font-size: 34rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 10rpx;
}

.user-location {
	display: flex;
	align-items: center;
}

.location-icon {
	font-size: 24rpx;
	color: #ffcb64;
	margin-right: 4rpx;
}

.location-text {
	font-size: 24rpx;
	color: #999;
}

.profile-arrow {
	padding: 10rpx;
}

.arrow-icon {
	color: #ccc;
	font-size: 60rpx;
}

/* 剩余时长卡片 */
.time-card {
	position: relative;
	border-radius: 16rpx;
	margin: 20rpx 0;
	overflow: hidden;
	background-color: rgba(255, 255, 255, 0.6);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	padding: 0;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.time-card-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
	background: linear-gradient(to right, #ffcb46, #ffcb64 40%, #fff3cf);
	border-radius: 16rpx;
	overflow: hidden;
}

.time-card-bg::before {
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 60%);
	animation: shine 8s linear infinite;
}

.time-card-bg::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect fill="none" width="100" height="100"/><rect fill-opacity="0.05" x="25" y="25" width="50" height="50" transform="rotate(45 50 50)"/></svg>');
	opacity: 0.3;
	animation: pattern 15s linear infinite;
}

@keyframes shine {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

@keyframes pattern {
	0% {
		background-position: 0 0;
	}

	100% {
		background-position: 100px 100px;
	}
}

.time-content {
	position: relative;
	z-index: 1;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	height: 120rpx;
	backdrop-filter: blur(5px);
	-webkit-backdrop-filter: blur(5px);
}

.time-info {
	color: #333;
	text-shadow: none;
}

.time-title {
	font-size: 26rpx;
	font-weight: 400;
	margin-bottom: 12rpx;
	text-shadow: none;
	color: #333;
}

.time-value {
	font-size: 48rpx;
	font-weight: bold;
	text-shadow: none;
	line-height: 1;
	color: #333;
}

.time-unit {
	font-size: 24rpx;
	font-weight: normal;
	text-shadow: none;
	color: #333;
}

.time-button {
	background-color: rgba(255, 255, 255, 0.5);
	border-radius: 30rpx;
	padding: 8rpx 20rpx;
	color: #333;
	font-size: 24rpx;
	text-shadow: none;
	font-weight: 500;
	border: 1rpx solid rgba(255, 255, 255, 0.7);
	backdrop-filter: blur(5px);
	-webkit-backdrop-filter: blur(5px);
	transition: all 0.3s ease;
}

.time-button:active {
	transform: scale(0.95);
	background-color: rgba(255, 255, 255, 0.7);
}

.time-warning {
	position: relative;
	z-index: 1;
	background-color: rgba(255, 255, 255, 0.3);
	backdrop-filter: blur(8px);
	-webkit-backdrop-filter: blur(8px);
	padding: 12rpx 30rpx;
	display: flex;
	align-items: center;
	border-top: 1rpx solid rgba(255, 255, 255, 0.3);
}

.warning-icon {
	color: #FF5252;
	font-size: 24rpx;
	margin-right: 10rpx;
	text-shadow: none;
}

.warning-text {
	color: #333;
	font-size: 22rpx;
	text-shadow: none;
	font-weight: 400;
}

/* 订单区域 */
.order-section {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx 0;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 30rpx 20rpx 30rpx;
	margin-bottom: 20rpx;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
}

.section-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
}

.view-all {
	font-size: 24rpx;
	color: #999;
	display: flex;
	align-items: center;
	padding: 6rpx 12rpx;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.view-all:active {
	background-color: #f5f5f5;
}

.order-status-tabs {
	display: flex;
	justify-content: space-between;
	padding: 10rpx 0 10rpx;
}

.status-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 25%;
	transition: transform 0.2s ease;
}

.status-item:active {
	transform: scale(0.95);
}


.status-image {
	width: 75rpx;
	height: 75rpx;
	transition: transform 0.2s ease;
}

.status-item:active .status-image {
	transform: scale(0.9);
}

.badge {
	position: absolute;
	top: -6rpx;
	right: -6rpx;
	background-color: #FF5252;
	color: #fff;
	font-size: 20rpx;
	min-width: 32rpx;
	height: 32rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 6rpx;
	box-shadow: 0 2rpx 4rpx rgba(255, 82, 82, 0.3);
	font-weight: 500;
}

.status-text {
	font-size: 24rpx;
	color: #666;
	transition: color 0.2s ease;
}

.status-item:active .status-text {
	color: #FF9500;
}

/* 服务菜单 */
.service-section {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 20rpx 0 0 0;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
	overflow: hidden;
}

.section-title {
	font-size: 28rpx;
	color: #666;
	padding: 10rpx 30rpx 20rpx;
}

.service-list {
	width: 100%;
}

.service-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 30rpx;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
}

.service-item:active {
	background-color: #f9f9f9;
}

.service-item:last-child {
	border-bottom: none;
}

.service-item-left {
	display: flex;
	align-items: center;
}

.service-item-title {
	font-size: 28rpx;
	color: #333;
	margin-left: 20rpx;
}

.service-item-right {
	display: flex;
	align-items: center;
	color: #999;
}

.service-item-value {
	font-size: 26rpx;
	margin-right: 10rpx;
}

.cell-icon {
	width: 70rpx;
	height: 70rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	color: #666;
	background-color: #f5f5f5;
}

.time-card-icon {
	background-color: #FF9500;
}

.cash-coupon-icon {
	background-color: #FF3B30;
}

.contact-icon {
	background-color: #34C759;
}

.agreement-icon {
	background-color: #007AFF;
}

/* 奖励计划 */
.rewards-menu {
	display: flex;
}

.rewards-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx 0;
	width: 25%;
}

.rewards-icon {
	background-color: #f8e9d2;
	color: #e6b422;
}


</style>
