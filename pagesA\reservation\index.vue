<template>
	<view class="coach-detail-container">
		<!-- 师傅信息卡片 -->
		<view class="coach-info-card">
			<!-- 背景装饰 -->
			<view class="card-background">
				<view class="bg-gradient"></view>
				<view class="bg-pattern"></view>
			</view>

			<!-- 师傅头像区域 -->
			<view class="coach-avatar-section">
				<view class="avatar-container">
					<image :src="coachInfo.avatar" mode="aspectFill" class="coach-avatar"></image>
					<view class="avatar-ring"></view>
				</view>
			</view>

			<!-- 师傅信息 -->
			<view class="coach-info-section">
				<view class="coach-header">
					<view class="coach-name">{{ coachInfo.name }}</view>
					<view class="coach-status-btn">
						<text>{{ coachInfo.status }}</text>
					</view>
				</view>

				<view class="coach-tags">
					<text class="tag" v-for="tag in coachInfo.tags" :key="tag">{{ tag }}</text>
				</view>

				<view class="coach-details">
					<view class="detail-item">
						<text class="ri-map-pin-line"></text>
						<text class="detail-text">{{ coachInfo.serviceArea }}</text>
					</view>
					<view class="detail-item">
						<text class="ri-user-star-line"></text>
						<text class="detail-text">已服务 {{ coachInfo.serviceCount }} 次</text>
					</view>
				</view>
			</view>

			<!-- 认证信息 -->
			<view class="certification-section">
				<view class="cert-card" @click="handleCertificationClick">
					<view class="cert-icon">
						<text class="ri-shield-check-line"></text>
					</view>
					<view class="cert-info">
						<view class="cert-title">平台认证老师</view>
						<view class="cert-desc">{{ coachInfo.certificationDesc }}</view>
					</view>
					<view class="cert-arrow">
						<text class="ri-arrow-right-s-line"></text>
					</view>
				</view>
			</view>
		</view>

		<!-- 选择预订时间 -->
		<view class="time-selection-section">
			<view class="section-title">
				<view class="title-line"></view>
				<text class="title-text">选择预订时间</text>
			</view>

			<!-- 日期选择 -->
			<scroll-view class="date-scroll-container" scroll-x="true" :scroll-into-view="scrollIntoView" show-scrollbar="false">
				<view class="date-selection">
					<view class="date-item"
						v-for="(date, index) in dateList"
						:key="index"
						:id="`date-${index}`"
						:class="{ 'date-active': selectedDateIndex === index }"
						@click="handleDateSelect(index)">
						<view class="date-weekday">{{ date.weekday }}</view>
						<view class="date-number">{{ date.date }}</view>
					</view>
				</view>
			</scroll-view>

			<!-- 时间段选择 -->
			<view class="time-slots-grid">
				<view class="time-slot-item"
					v-for="(slot, index) in timeSlots"
					:key="index"
					:class="getTimeSlotClass(slot)"
					@click="handleTimeSlotSelect(slot)">
					<view class="time-text">{{ slot.startTime }}-{{ slot.endTime }}</view>
					<view class="status-text">{{ slot.statusText }}</view>
				</view>
			</view>

			<!-- 已选择的时间显示 -->
			<view class="selected-time-display" v-if="selectedTimeSlots.length > 0">
				<text class="selected-prefix">已选:</text>
				<text class="selected-time-text">{{ formatSelectedTime() }}</text>
				<view class="duration-hint" v-if="selectedTimeSlots.length < reservationConfig.minDuration * 2">
					<text class="hint-text">还需选择 {{ reservationConfig.minDuration * 2 - selectedTimeSlots.length }} 个时间段</text>
				</view>
			</view>
		</view>

		<!-- 底部操作按钮 -->
		<view class="bottom-actions">
			<wd-button type="primary" size="large" custom-style="background: #FF9500; border-color: #FF9500;"
				:disabled="selectedTimeSlots.length === 0" @click="handleNextStep">
				下一步
			</wd-button>
		</view>

		<!-- 底部安全区域 -->
		<view class="safe-area-bottom"></view>
	</view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'

// 页面参数
const coachId = ref('')

// 师傅信息
const coachInfo = ref({
	id: '',
	name: '赵航挺',
	avatar: '/static/images/2.png',
	status: '较忙碌', // 司机状态
	tags: ['轿车', '燃油车', '丰田凯美瑞'],
	serviceArea: '上城区,余杭区,拱墅区,西湖区',
	serviceCount: 449,
	certificationDesc: '2项职业认证/1辆认证车辆',
	rating: '278条好评'
})

// 日期相关
const selectedDateIndex = ref(0)
const dateList = ref([])
const scrollIntoView = ref('')

// 时间段相关
const selectedTimeSlots = ref([]) // 改为数组，支持多选
const timeSlots = ref([])

// 预约配置
const reservationConfig = {
	minDuration: 2, // 最少选择2小时
	maxDuration: 4  // 最多选择4小时
}

// 营业时间配置
const businessHours = {
	start: 9, // 9:00
	end: 23,  // 23:00
	interval: 30 // 30分钟间隔
}

// 生成未来一周的日期
const generateDateList = () => {
	const dates = []
	const today = new Date()

	for (let i = 0; i < 7; i++) {
		const date = new Date(today)
		date.setDate(today.getDate() + i)

		const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
		const month = String(date.getMonth() + 1).padStart(2, '0')
		const day = String(date.getDate()).padStart(2, '0')

		dates.push({
			date: `${month}-${day}`,
			weekday: weekdays[date.getDay()],
			fullDate: date
		})
	}

	dateList.value = dates
}

// 生成时间段
const generateTimeSlots = () => {
	const slots = []
	const { start, end, interval } = businessHours
	let index = 0

	for (let hour = start; hour < end; hour++) {
		for (let minute = 0; minute < 60; minute += interval) {
			const startTime = `${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}`

			let endHour = hour
			let endMinute = minute + interval

			if (endMinute >= 60) {
				endHour += 1
				endMinute = 0
			}

			const endTime = `${String(endHour).padStart(2, '0')}:${String(endMinute).padStart(2, '0')}`

			// 模拟时间段状态
			const status = getTimeSlotStatus(hour, minute)

			slots.push({
				index, // 添加索引，用于判断连续性
				startTime,
				endTime,
				status,
				statusText: getStatusText(status),
				available: status === 'available'
			})

			index++
		}
	}

	timeSlots.value = slots
}

// 获取时间段状态（模拟数据）
const getTimeSlotStatus = (hour, minute) => {
	// 只有两种状态：可约和已约
	// 模拟一些时间段已被预约
	if (hour >= 14 && hour < 16) return 'booked' // 14-16点已约
	if (hour >= 19 && hour < 21) return 'booked' // 19-21点已约

	// 其他时间随机状态
	const random = Math.random()
	if (random < 0.3) return 'booked' // 30% 已约
	return 'available' // 70% 可约
}

// 获取状态文本
const getStatusText = (status) => {
	switch (status) {
		case 'available': return '可约'
		case 'booked': return '已约'
		case 'selected': return '已选'
		default: return '可约'
	}
}

// 获取时间段样式类
const getTimeSlotClass = (slot) => {
	return `slot-${slot.status}`
}

// 处理日期选择
const handleDateSelect = (index) => {
	selectedDateIndex.value = index
	selectedTimeSlots.value = [] // 切换日期时清空选中的时间

	// 自动滚动到选中的日期
	scrollIntoView.value = `date-${index}`

	// 重新生成时间段（可以根据日期获取不同的可用时间）
	generateTimeSlots()
}

// 处理时间段选择
const handleTimeSlotSelect = (slot) => {
	// 如果是已约状态，不允许选择
	if (slot.status === 'booked') return
	if (!slot.available) return

	// 如果已经选中，则取消选择
	if (slot.status === 'selected') {
		removeTimeSlot(slot)
		return
	}

	// 添加时间段到选择列表
	addTimeSlot(slot)
}

// 添加时间段
const addTimeSlot = (slot) => {
	const newSelection = [...selectedTimeSlots.value, slot].sort((a, b) => a.index - b.index)

	// 检查是否连续
	if (isConsecutive(newSelection)) {
		selectedTimeSlots.value = newSelection
		updateSlotsDisplay()
	} else {
		// 如果不连续，提示用户
		uni.showToast({
			title: '请选择连续的时间段',
			icon: 'none'
		})
	}
}

// 移除时间段
const removeTimeSlot = (slot) => {
	selectedTimeSlots.value = selectedTimeSlots.value.filter(s => s.index !== slot.index)
	updateSlotsDisplay()
}

// 检查时间段是否连续
const isConsecutive = (slots) => {
	if (slots.length <= 1) return true

	for (let i = 1; i < slots.length; i++) {
		if (slots[i].index !== slots[i - 1].index + 1) {
			return false
		}
	}
	return true
}

// 更新时间段显示状态
const updateSlotsDisplay = () => {
	// 重置所有可用时间段的状态
	timeSlots.value.forEach(slot => {
		if (slot.status === 'selected') {
			slot.status = 'available'
			slot.statusText = '可约'
		}
	})

	// 设置选中时间段的状态
	selectedTimeSlots.value.forEach(selectedSlot => {
		const slot = timeSlots.value.find(s => s.index === selectedSlot.index)
		if (slot) {
			slot.status = 'selected'
			slot.statusText = '已选'
		}
	})

	// 检查是否满足最少时长要求
	checkMinDuration()
}

// 检查最少时长
const checkMinDuration = () => {
	const selectedCount = selectedTimeSlots.value.length
	const minSlots = reservationConfig.minDuration * 2 // 每小时2个时间段

	if (selectedCount > 0 && selectedCount < minSlots) {
		// 可以显示提示，但不阻止选择
		console.log(`还需选择 ${minSlots - selectedCount} 个时间段才能预约`)
	}
}

// 格式化选中的时间
const formatSelectedTime = () => {
	if (selectedTimeSlots.value.length === 0) return ''

	const sortedSlots = selectedTimeSlots.value.sort((a, b) => a.index - b.index)
	const startTime = sortedSlots[0].startTime
	const endTime = sortedSlots[sortedSlots.length - 1].endTime
	const duration = selectedTimeSlots.value.length * 0.5 // 每个时间段0.5小时

	return `${startTime}-${endTime}(${duration}小时)`
}

// 计算时长
const calculateDuration = (startTime, endTime) => {
	const [startHour, startMinute] = startTime.split(':').map(Number)
	const [endHour, endMinute] = endTime.split(':').map(Number)

	const startMinutes = startHour * 60 + startMinute
	const endMinutes = endHour * 60 + endMinute

	const durationMinutes = endMinutes - startMinutes
	return (durationMinutes / 60).toFixed(1)
}

// 处理下一步
const handleNextStep = () => {
	if (selectedTimeSlots.value.length === 0) {
		uni.showToast({
			title: '请选择预订时间',
			icon: 'none'
		})
		return
	}

	// 检查是否满足最少时长要求
	const minSlots = reservationConfig.minDuration * 2
	if (selectedTimeSlots.value.length < minSlots) {
		uni.showToast({
			title: `至少需要选择${reservationConfig.minDuration}小时连续时间`,
			icon: 'none'
		})
		return
	}

	// 准备预约信息
	const selectedDate = dateList.value[selectedDateIndex.value]
	const sortedSlots = selectedTimeSlots.value.sort((a, b) => a.index - b.index)
	const duration = selectedTimeSlots.value.length * 0.5

	// 格式化日期为 2025-07-23 格式
	const formattedDate = selectedDate.fullDate.toISOString().split('T')[0]
	const startTime = sortedSlots[0].startTime
	const endTime = sortedSlots[sortedSlots.length - 1].endTime

	// 跳转到预约详情页面，传递预约信息
	const queryParams = {
		type: 'reservation', // 标识这是预约详情页面
		coachId: coachInfo.value.id,
		coachName: encodeURIComponent(coachInfo.value.name),
		coachAvatar: encodeURIComponent(coachInfo.value.avatar),
		date: formattedDate,
		startTime: startTime,
		endTime: endTime,
		duration: duration
	}

	// 构建查询字符串
	const queryString = Object.keys(queryParams)
		.map(key => `${key}=${queryParams[key]}`)
		.join('&')

	uni.navigateTo({
		url: `/pagesA/booking/index?${queryString}`
	})
}

// 处理认证信息点击
const handleCertificationClick = () => {
	uni.navigateTo({
		url: `/pagesA/authentication/index?coachId=${coachInfo.value.id}&coachName=${encodeURIComponent(coachInfo.value.name)}`
	})
}

// 页面加载
onMounted(() => {
	// 获取页面参数
	const pages = getCurrentPages()
	const currentPage = pages[pages.length - 1]
	coachId.value = currentPage.options.id || ''

	// 初始化数据
	generateDateList()
	generateTimeSlots()

	// TODO: 根据 coachId 获取师傅详细信息
	loadCoachInfo()
})

// 加载师傅信息
const loadCoachInfo = async () => {
	try {
		// TODO: 调用接口获取师傅信息
		// const response = await api.getCoachDetail(coachId.value)
		// coachInfo.value = response.data

		console.log('加载师傅信息:', coachId.value)
	} catch (error) {
		console.error('加载师傅信息失败:', error)
	}
}
</script>

<style lang="scss" scoped>
.coach-detail-container {
	background: #f8f9fa;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 师傅信息卡片 - 现代毛玻璃设计 */
.coach-info-card {
	background: #fff;
	margin: 32rpx;
	border-radius: 32rpx;
	position: relative;
	overflow: hidden;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

/* 背景装饰 */
.card-background {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 200rpx;
	overflow: hidden;
}

.bg-gradient {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 100%;
	background: linear-gradient(135deg,
		rgba(255, 149, 0, 0.08) 0%,
		rgba(255, 184, 0, 0.05) 50%,
		rgba(255, 149, 0, 0.03) 100%);
}

.bg-pattern {
	position: absolute;
	top: -50rpx;
	right: -50rpx;
	width: 200rpx;
	height: 200rpx;
	background: radial-gradient(circle, rgba(255, 149, 0, 0.1) 0%, transparent 70%);
	border-radius: 50%;
}

/* 师傅头像区域 */
.coach-avatar-section {
	position: relative;
	z-index: 2;
	padding: 40rpx 32rpx 0 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
}

.avatar-container {
	position: relative;
	margin-bottom: 16rpx;
}

.coach-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	border: 4rpx solid #fff;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.avatar-ring {
	position: absolute;
	top: -8rpx;
	left: -8rpx;
	right: -8rpx;
	bottom: -8rpx;
	border: 2rpx solid rgba(255, 149, 0, 0.3);
	border-radius: 50%;
	animation: pulse 2s infinite;
}

@keyframes pulse {
	0% { transform: scale(1); opacity: 1; }
	50% { transform: scale(1.05); opacity: 0.7; }
	100% { transform: scale(1); opacity: 1; }
}



/* 师傅信息区域 */
.coach-info-section {
	padding: 24rpx 32rpx;
	position: relative;
	z-index: 2;
}

.coach-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.coach-name {
	font-size: 48rpx;
	font-weight: 700;
	color: #333;
	flex: 1;
}

.coach-status-btn {
	background: rgba(255, 149, 0, 0.1);
	color: #FF9500;
	font-size: 24rpx;
	font-weight: 600;
	padding: 12rpx 20rpx;
	border-radius: 24rpx;
	border: 1rpx solid rgba(255, 149, 0, 0.2);
	backdrop-filter: blur(10rpx);
}

.coach-tags {
	display: flex;
	justify-content: center;
	gap: 12rpx;
	margin-bottom: 20rpx;
	flex-wrap: wrap;
}

.tag {
	background: rgba(255, 149, 0, 0.08);
	color: #FF9500;
	font-size: 22rpx;
	font-weight: 500;
	padding: 8rpx 16rpx;
	border-radius: 16rpx;
	border: 1rpx solid rgba(255, 149, 0, 0.15);
}

.coach-details {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.detail-item {
	display: flex;
	align-items: center;
	gap: 12rpx;
	font-size: 26rpx;
	color: #666;
}

.detail-item .ri-map-pin-line,
.detail-item .ri-user-star-line {
	font-size: 28rpx;
	color: #FF9500;
}

.detail-text {
	flex: 1;
}

/* 认证信息区域 */
.certification-section {
	padding: 0 32rpx 32rpx 32rpx;
	position: relative;
	z-index: 2;
}

.cert-card {
	background: rgba(255, 255, 255, 0.8);
	backdrop-filter: blur(20rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 20rpx;
	padding: 24rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.cert-icon {
	width: 48rpx;
	height: 48rpx;
	background: linear-gradient(135deg, #FF9500 0%, #FFB800 100%);
	border-radius: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
	font-size: 24rpx;
	color: #fff;
	box-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.3);
}

.cert-info {
	flex: 1;
}

.cert-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 4rpx;
}

.cert-desc {
	font-size: 22rpx;
	color: #999;
}

.cert-arrow {
	font-size: 24rpx;
	color: #ccc;
}

/* 时间选择区域 */
.time-selection-section {
	background: #fff;
	margin-top: 24rpx;
	padding: 32rpx;
	border-radius: 32rpx 32rpx 0 0;
}

.section-title {
	display: flex;
	align-items: center;
	margin-bottom: 32rpx;
}

.title-line {
	width: 8rpx;
	height: 36rpx;
	background: #FF9500;
	border-radius: 4rpx;
	margin-right: 16rpx;
}

.title-text {
	font-size: 36rpx;
	font-weight: 700;
	color: #333;
}

/* 日期选择 - 横向滚动 */
.date-scroll-container {
	width: 100%;
	white-space: nowrap;
	margin-bottom: 40rpx;
}

.date-selection {
	display: inline-flex;
	padding: 0 16rpx;
}

.date-item {
	min-width: 120rpx;
	text-align: center;
	padding: 20rpx 16rpx;
	border-radius: 16rpx;
	background: #f8f9fa;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
	margin-right: 24rpx;
	position: relative;
}

.date-item:after {
	content: '';
	position: absolute;
	bottom: -8rpx;
	left: 50%;
	transform: translateX(-50%);
	width: 0;
	height: 6rpx;
	background: #FF9500;
	border-radius: 3rpx;
	transition: all 0.3s ease;
}

.date-item.date-active {
	background: rgba(255, 149, 0, 0.05);
	border-color: rgba(255, 149, 0, 0.15);
	box-shadow: 0 2rpx 8rpx rgba(255, 149, 0, 0.1);
}

.date-item.date-active:after {
	width: 40rpx;
}

.date-weekday {
	font-size: 28rpx;
	font-weight: 600;
	margin-bottom: 8rpx;
	color: #666;
}

.date-number {
	font-size: 24rpx;
	color: #999;
}

.date-item.date-active .date-weekday {
	color: #666;
}

.date-item.date-active .date-number {
	color: #FF9500;
	font-weight: 500;
}

/* 时间段网格 */
.time-slots-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 16rpx;
	margin-bottom: 40rpx;
}

.time-slot-item {
	background: #f8f9fa;
	border: 2rpx solid #f0f0f0;
	border-radius: 12rpx;
	padding: 18rpx 12rpx;
	text-align: center;
	transition: all 0.3s ease;
	position: relative;
	min-height: 80rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.time-text {
	font-size: 24rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 4rpx;
}

.status-text {
	font-size: 20rpx;
	color: #999;
}

/* 时间段状态样式 */
.slot-available {
	background: #fff;
	border-color: #e8e8e8;
	cursor: pointer;
}

.slot-available:active {
	transform: scale(0.96);
}

.slot-available .time-text {
	color: #333;
}

.slot-available .status-text {
	color: #666;
	font-weight: 500;
}

.slot-selected {
	background: rgba(255, 149, 0, 0.1);
	border-color: rgba(255, 149, 0, 0.3);
	box-shadow: 0 2rpx 8rpx rgba(255, 149, 0, 0.15);
}

.slot-selected .time-text {
	color: #FF9500;
	font-weight: 600;
}

.slot-selected .status-text {
	color: #FF9500;
}

.slot-booked {
	background: #f5f5f5;
	border-color: #e8e8e8;
	opacity: 0.6;
	cursor: not-allowed;
}

.slot-booked .time-text {
	color: #bbb;
}

.slot-booked .status-text {
	color: #999;
}

/* 已选择时间显示 */
.selected-time-display {
	background: linear-gradient(135deg, rgba(255, 149, 0, 0.1) 0%, rgba(255, 184, 0, 0.1) 100%);
	border: 2rpx solid #FF9500;
	border-radius: 20rpx;
	padding: 24rpx;
	text-align: center;
	position: relative;
	overflow: hidden;
}

.selected-time-display::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
	animation: shimmer 2s infinite;
}

@keyframes shimmer {
	0% { transform: translateX(-100%); }
	100% { transform: translateX(100%); }
}

.selected-prefix {
	font-size: 28rpx;
	color: #666;
	margin-right: 12rpx;
	font-weight: 500;
}

.selected-time-text {
	font-size: 32rpx;
	font-weight: 700;
	color: #FF9500;
}

.duration-hint {
	margin-top: 12rpx;
	text-align: center;
}

.hint-text {
	font-size: 24rpx;
	color: #999;
}

/* 底部操作按钮 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding: 32rpx;
	border-top: 1rpx solid #f0f0f0;
	z-index: 100;
	box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);
}

/* 底部安全区域 */
.safe-area-bottom {
	height: env(safe-area-inset-bottom);
	background: #fff;
}
</style>