<template>
    <view class="profile-container">
        <!-- 个人信息内容 -->
        <view class="profile-content">
            <!-- 头像区域 -->
            <view class="avatar-section">
                <view class="avatar-container" @click="handleAvatarEdit">
                    <image class="avatar-image" :src="userInfo.avatar" mode="aspectFill" />
                    <view class="avatar-edit-btn">
                        <text class="ri-camera-line"></text>
                    </view>
                </view>
                <text class="avatar-tip">点击更换头像</text>
            </view>

            <!-- 基本信息区域 -->
            <view class="info-section">
                <!-- 昵称 -->
                <view class="info-item" @click="handleNicknameEdit">
                    <view class="item-icon">
                        <text class="ri-user-line"></text>
                    </view>
                    <view class="item-content">
                        <text class="item-label">昵称</text>
                        <text class="item-value">{{ userInfo.nickname || '未设置' }}</text>
                    </view>
                    <text class="ri-arrow-right-s-line arrow-icon"></text>
                </view>

                <!-- 手机号 -->
                <view class="info-item" @click="handlePhoneEdit">
                    <view class="item-icon">
                        <text class="ri-phone-line"></text>
                    </view>
                    <view class="item-content">
                        <text class="item-label">手机号</text>
                        <text class="item-value" :class="{ 'bound': userInfo.phone }">
                            {{ userInfo.phone ? formatPhone(userInfo.phone) : '未绑定' }}
                        </text>
                    </view>
                    <view class="item-status">
                        <text class="bound-badge" v-if="userInfo.phone">已绑定</text>
                        <text class="ri-arrow-right-s-line arrow-icon" v-else></text>
                    </view>
                </view>

                <!-- 注册时间 -->
                <view class="info-item">
                    <view class="item-icon">
                        <text class="ri-calendar-line"></text>
                    </view>
                    <view class="item-content">
                        <text class="item-label">注册时间</text>
                        <text class="item-value">{{ formatDate(userInfo.registerTime) }}</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 昵称编辑弹窗 -->
        <wd-popup v-model="showNicknameModal" position="center" :close-on-click-modal="false">
            <view class="edit-modal">
                <view class="modal-header">
                    <text class="modal-title">修改昵称</text>
                </view>
                <view class="modal-content">
                    <view class="input-container">
                        <input
                            v-model="editNickname"
                            placeholder="请输入昵称"
                            maxlength="20"
                            class="edit-input"
                            focus
                        />
                        <view class="char-count">{{ editNickname.length }}/20</view>
                    </view>
                </view>
                <view class="modal-actions">
                    <button class="action-btn cancel" @click="cancelNicknameEdit">取消</button>
                    <button class="action-btn confirm" @click="confirmNicknameEdit" :disabled="!editNickname.trim()">确定</button>
                </view>
            </view>
        </wd-popup>

        <!-- 手机号绑定弹窗 -->
        <wd-popup v-model="showPhoneModal" position="center" :close-on-click-modal="false">
            <view class="edit-modal">
                <view class="modal-header">
                    <text class="modal-title">绑定手机号</text>
                </view>
                <view class="modal-content">
                    <view class="input-container">
                        <input
                            v-model="editPhone"
                            placeholder="请输入手机号"
                            type="number"
                            maxlength="11"
                            class="edit-input"
                        />
                    </view>
                    <view class="verify-section">
                        <view class="input-container">
                            <input
                                v-model="verifyCode"
                                placeholder="请输入验证码"
                                maxlength="6"
                                class="edit-input"
                            />
                        </view>
                        <button
                            class="send-code-btn"
                            :disabled="!canSendCode || countdown > 0"
                            @click="sendVerifyCode"
                        >
                            {{ countdown > 0 ? `${countdown}s` : '发送验证码' }}
                        </button>
                    </view>
                </view>
                <view class="modal-actions">
                    <button class="action-btn cancel" @click="cancelPhoneEdit">取消</button>
                    <button class="action-btn confirm" @click="confirmPhoneEdit">确定</button>
                </view>
            </view>
        </wd-popup>
    </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 用户信息数据
const userInfo = ref({
    avatar: '/static/images/default-avatar.png', // 默认头像
    nickname: '里派用户',
    phone: '', // 空表示未绑定，有值表示已绑定
    registerTime: '2024-01-15'
})

// 弹窗控制
const showNicknameModal = ref(false)
const showPhoneModal = ref(false)

// 编辑数据
const editNickname = ref('')
const editPhone = ref('')
const verifyCode = ref('')
const countdown = ref(0)

// 验证码发送条件
const canSendCode = computed(() => {
    return editPhone.value && /^1[3-9]\d{9}$/.test(editPhone.value)
})

// 页面加载时获取用户信息
onMounted(() => {
    loadUserInfo()
})

// 加载用户信息
const loadUserInfo = async () => {
    try {
        // 这里应该调用实际的API获取用户信息
        // const res = await uni.request({ url: '/api/user/info' })
        // userInfo.value = res.data

        // 模拟数据
        userInfo.value = {
            avatar: '/static/images/2.png',
            nickname: '里派用户',
            phone: '13800138000', // 示例：已绑定手机号
            registerTime: '2024-01-15'
        }
    } catch (error) {
        console.error('获取用户信息失败:', error)
    }
}

// 处理头像编辑
const handleAvatarEdit = () => {
    uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
            const tempFilePath = res.tempFilePaths[0]
            // 这里应该上传图片到服务器
            uploadAvatar(tempFilePath)
        },
        fail: (error) => {
            console.error('选择图片失败:', error)
        }
    })
}

// 上传头像
const uploadAvatar = async (filePath) => {
    try {
        uni.showLoading({ title: '上传中...' })

        // 这里应该调用实际的上传API
        // const uploadRes = await uni.uploadFile({
        //     url: '/api/upload/avatar',
        //     filePath: filePath,
        //     name: 'avatar'
        // })

        // 模拟上传成功
        setTimeout(() => {
            userInfo.value.avatar = filePath
            uni.hideLoading()
            uni.showToast({
                title: '头像更新成功',
                icon: 'success'
            })
        }, 1000)

    } catch (error) {
        uni.hideLoading()
        uni.showToast({
            title: '上传失败',
            icon: 'error'
        })
        console.error('上传头像失败:', error)
    }
}

// 处理昵称编辑
const handleNicknameEdit = () => {
    editNickname.value = userInfo.value.nickname || ''
    showNicknameModal.value = true
}

// 取消昵称编辑
const cancelNicknameEdit = () => {
    showNicknameModal.value = false
    editNickname.value = ''
}

// 确认昵称编辑
const confirmNicknameEdit = async () => {
    if (!editNickname.value.trim()) {
        uni.showToast({
            title: '请输入昵称',
            icon: 'error'
        })
        return
    }

    try {
        uni.showLoading({ title: '保存中...' })

        // 这里应该调用实际的API更新昵称
        // await uni.request({
        //     url: '/api/user/update',
        //     method: 'POST',
        //     data: { nickname: editNickname.value }
        // })

        // 模拟更新成功
        setTimeout(() => {
            userInfo.value.nickname = editNickname.value
            showNicknameModal.value = false
            editNickname.value = ''
            uni.hideLoading()
            uni.showToast({
                title: '昵称更新成功',
                icon: 'success'
            })
        }, 500)

    } catch (error) {
        uni.hideLoading()
        uni.showToast({
            title: '更新失败',
            icon: 'error'
        })
        console.error('更新昵称失败:', error)
    }
}

// 处理手机号编辑
const handlePhoneEdit = () => {
    if (userInfo.value.phone) {
        // 已绑定手机号，显示提示
        uni.showModal({
            title: '提示',
            content: '手机号已绑定，如需更换请联系客服',
            showCancel: false
        })
        return
    }

    // 未绑定，打开绑定弹窗
    editPhone.value = ''
    verifyCode.value = ''
    showPhoneModal.value = true
}

// 取消手机号编辑
const cancelPhoneEdit = () => {
    showPhoneModal.value = false
    editPhone.value = ''
    verifyCode.value = ''
    countdown.value = 0
}

// 发送验证码
const sendVerifyCode = async () => {
    if (!canSendCode.value) {
        return
    }

    try {
        // 这里应该调用实际的API发送验证码
        // await uni.request({
        //     url: '/api/sms/send',
        //     method: 'POST',
        //     data: { phone: editPhone.value }
        // })

        // 模拟发送成功
        uni.showToast({
            title: '验证码已发送',
            icon: 'success'
        })

        // 开始倒计时
        countdown.value = 60
        const timer = setInterval(() => {
            countdown.value--
            if (countdown.value <= 0) {
                clearInterval(timer)
            }
        }, 1000)

    } catch (error) {
        uni.showToast({
            title: '发送失败',
            icon: 'error'
        })
        console.error('发送验证码失败:', error)
    }
}

// 确认手机号绑定
const confirmPhoneEdit = async () => {
    if (!editPhone.value) {
        uni.showToast({
            title: '请输入手机号',
            icon: 'error'
        })
        return
    }

    if (!canSendCode.value) {
        uni.showToast({
            title: '请输入正确的手机号',
            icon: 'error'
        })
        return
    }

    if (!verifyCode.value) {
        uni.showToast({
            title: '请输入验证码',
            icon: 'error'
        })
        return
    }

    try {
        uni.showLoading({ title: '绑定中...' })

        // 这里应该调用实际的API绑定手机号
        // await uni.request({
        //     url: '/api/user/bind-phone',
        //     method: 'POST',
        //     data: {
        //         phone: editPhone.value,
        //         code: verifyCode.value
        //     }
        // })

        // 模拟绑定成功
        setTimeout(() => {
            userInfo.value.phone = editPhone.value
            showPhoneModal.value = false
            editPhone.value = ''
            verifyCode.value = ''
            countdown.value = 0
            uni.hideLoading()
            uni.showToast({
                title: '绑定成功',
                icon: 'success'
            })
        }, 500)

    } catch (error) {
        uni.hideLoading()
        uni.showToast({
            title: '绑定失败',
            icon: 'error'
        })
        console.error('绑定手机号失败:', error)
    }
}

// 格式化手机号显示
const formatPhone = (phone) => {
    if (!phone) return ''
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

// 格式化日期显示
const formatDate = (dateStr) => {
    if (!dateStr) return ''
    const date = new Date(dateStr)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}
</script>

<style lang="scss" scoped>
page {
    background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
}

.profile-container {
    min-height: 100vh;
    background: transparent;
}

.profile-content {
    padding: 32rpx;
}

/* 头像区域 */
.avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 60rpx 0;
    margin-bottom: 40rpx;
}

.avatar-container {
    position: relative;
    margin-bottom: 16rpx;
}

.avatar-image {
    width: 160rpx;
    height: 160rpx;
    border-radius: 80rpx;
    border: 6rpx solid #fff;
    box-shadow:
        0 8rpx 32rpx rgba(0, 0, 0, 0.1),
        0 0 0 1rpx rgba(255, 149, 0, 0.1);
}

.avatar-edit-btn {
    position: absolute;
    bottom: 4rpx;
    right: 4rpx;
    width: 52rpx;
    height: 52rpx;
    background: linear-gradient(135deg, #FF9500 0%, #ff7b00 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 16rpx rgba(255, 149, 0, 0.3);
    border: 4rpx solid #fff;

    .ri-camera-line {
        font-size: 24rpx;
        color: #fff;
        line-height: 1;
    }
}

.avatar-tip {
    font-size: 26rpx;
    color: #999;
}

/* 信息区域 */
.info-section {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(248, 250, 255, 0.95) 100%);
    backdrop-filter: blur(20rpx);
    -webkit-backdrop-filter: blur(20rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    border-radius: 24rpx;
    overflow: hidden;
    box-shadow:
        0 8rpx 32rpx rgba(0, 0, 0, 0.08),
        0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.info-item {
    display: flex;
    align-items: center;
    padding: 28rpx 32rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
    transition: background-color 0.3s ease;
    min-height: 120rpx;

    &:last-child {
        border-bottom: none;
    }

    &:active {
        background: rgba(255, 149, 0, 0.05);
    }
}

.item-icon {
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;
    flex-shrink: 0;
    box-shadow: 0 4rpx 16rpx rgba(100, 116, 139, 0.2);

    .ri-user-line,
    .ri-phone-line,
    .ri-calendar-line {
        font-size: 28rpx;
        color: #fff;
        line-height: 1;
    }
}

.item-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 4rpx;
    min-height: 64rpx;
}

.item-label {
    font-size: 26rpx;
    color: #666;
    font-weight: 500;
    line-height: 1.2;
}

.item-value {
    font-size: 30rpx;
    color: #1a1a1a;
    font-weight: 600;
    line-height: 1.3;

    &.bound {
        color: #1a1a1a;
    }
}

.item-status {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 64rpx;
}

.bound-badge {
    font-size: 20rpx;
    color: #22c55e;
    background: rgba(34, 197, 94, 0.1);
    border: 1rpx solid rgba(34, 197, 94, 0.2);
    border-radius: 12rpx;
    padding: 8rpx 16rpx;
    font-weight: 600;
    line-height: 1;
}

.arrow-icon {
    font-size: 28rpx;
    color: #ccc;
    line-height: 1;
}

/* 弹窗样式 */
.edit-modal {
    width: 600rpx;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(248, 250, 255, 0.95) 100%);
    backdrop-filter: blur(20rpx);
    -webkit-backdrop-filter: blur(20rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    border-radius: 24rpx;
    overflow: hidden;
    box-shadow:
        0 20rpx 60rpx rgba(0, 0, 0, 0.15),
        0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.modal-header {
    padding: 40rpx 32rpx 24rpx;
    text-align: center;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
}

.modal-title {
    font-size: 32rpx;
    font-weight: 700;
    color: #1a1a1a;
}

.modal-content {
    padding: 32rpx;
}

.input-container {
    position: relative;
    margin-bottom: 24rpx;
}

.edit-input {
    width: 100%;
    height: 88rpx;
    padding: 0 24rpx;
    border-radius: 16rpx;
    background: rgba(255, 255, 255, 0.8);
    border: 2rpx solid rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    font-size: 28rpx;
    color: #1a1a1a;
    box-sizing: border-box;
}

.edit-input:focus {
    border-color: #FF9500;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 4rpx rgba(255, 149, 0, 0.1);
    outline: none;
}

.char-count {
    position: absolute;
    right: 24rpx;
    bottom: -32rpx;
    font-size: 22rpx;
    color: #999;
}

.verify-section {
    display: flex;
    align-items: flex-start;
    gap: 16rpx;
    margin-top: 16rpx;
}

.send-code-btn {
    height: 88rpx;
    padding: 0 24rpx;
    background: linear-gradient(135deg, #FF9500 0%, #ff7b00 100%);
    color: #fff;
    border: none;
    border-radius: 16rpx;
    font-size: 24rpx;
    font-weight: 600;
    white-space: nowrap;
    flex-shrink: 0;
    transition: all 0.3s ease;
    box-shadow: 0 4rpx 16rpx rgba(255, 149, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    min-width: 160rpx;
}

.send-code-btn:disabled {
    background: #ccc;
    box-shadow: none;
}

.send-code-btn:not(:disabled):active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 8rpx rgba(255, 149, 0, 0.4);
}

.modal-actions {
    padding: 24rpx 32rpx 32rpx;
    display: flex;
    gap: 16rpx;
    border-top: 1rpx solid rgba(0, 0, 0, 0.06);
}

.action-btn {
    flex: 1;
    height: 88rpx;
    border-radius: 16rpx;
    font-size: 28rpx;
    font-weight: 600;
    border: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.action-btn.cancel {
    background: rgba(255, 255, 255, 0.8);
    color: #666;
    border: 2rpx solid rgba(0, 0, 0, 0.1);
}

.action-btn.cancel:active {
    background: rgba(255, 255, 255, 0.6);
    transform: translateY(2rpx);
}

.action-btn.confirm {
    background: linear-gradient(135deg, #FF9500 0%, #ff7b00 100%);
    color: #fff;
    box-shadow: 0 4rpx 16rpx rgba(255, 149, 0, 0.3);
}

.action-btn.confirm:disabled {
    background: #ccc;
    box-shadow: none;
}

.action-btn.confirm:not(:disabled):active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 8rpx rgba(255, 149, 0, 0.4);
}

/* 响应式调整 */
@media (max-width: 750rpx) {
    .profile-content {
        padding: 24rpx;
    }

    .avatar-section {
        padding: 40rpx 0;
    }

    .avatar-image {
        width: 140rpx;
        height: 140rpx;
        border-radius: 70rpx;
    }

    .info-item {
        padding: 24rpx 28rpx;
        min-height: 100rpx;
    }

    .item-icon {
        width: 56rpx;
        height: 56rpx;

        .ri-user-line,
        .ri-phone-line,
        .ri-calendar-line {
            font-size: 24rpx;
        }
    }

    .edit-modal {
        width: 90vw;
        max-width: 500rpx;
    }
}
</style>