import CryptoJS from 'crypto-js';

const tools = {}

tools.storage = {
	set(cacheKey, data, expireIn = 0) {
		let cacheValue = {
			content: data,
			expireIn: expireIn === 0 ? 0 : new Date().getTime() + expireIn * 1000
		}
		return uni.setStorageSync(cacheKey, tools.base64.encrypt(JSON.stringify(cacheValue)))
	},
	get(cacheKey) {
		try {
			const cacheValue = JSON.parse(tools.base64.decrypt(uni.getStorageSync(cacheKey)))
			if (cacheValue) {
				let nowTime = new Date().getTime()
				if (nowTime > cacheValue.expireIn && cacheValue.expireIn !== 0) {
					localStorage.removeItem(cacheKey)
					return null;
				}
				return cacheValue.content
			}
			return null
		} catch (err) {
			return null
		}
	},
	remove(cacheKey) {
		return uni.removeStorageSync(cacheKey);
	},
	clear() {
		return uni.clearStorageSync();
	},
	info() {
		return uni.getStorageInfoSync();
	}
}

/* 复制对象 */
tools.objCopy = function(obj) {
	return JSON.parse(JSON.stringify(obj));
}

/* 日期格式化 */
tools.dateFormat = function(date, fmt = 'yyyy-MM-dd hh:mm:ss') {
	date = new Date(date)
	var o = {
		"M+": date.getMonth() + 1, //月份
		"d+": date.getDate(), //日
		"h+": date.getHours(), //小时
		"m+": date.getMinutes(), //分
		"s+": date.getSeconds(), //秒
		"q+": Math.floor((date.getMonth() + 3) / 3), //季度
		"S": date.getMilliseconds() //毫秒
	};
	if (/(y+)/.test(fmt)) {
		fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
	}
	for (var k in o) {
		if (new RegExp("(" + k + ")").test(fmt)) {
			fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k])
				.length)));
		}
	}
	return fmt;
}

tools.timestamp = function() {
	return Date.parse(new Date());
}

/* 常用加解密 */
tools.crypto = {
	//MD5加密
	MD5(data) {
		return CryptoJS.MD5(data).toString()
	},
	//AES加解密
	AES: {
		encrypt(data, secretKey, config = {}) {
			if (secretKey.length % 8 != 0) {
				console.warn("[SCUI error]: 秘钥长度需为8的倍数，否则解密将会失败。")
			}
			const result = CryptoJS.AES.encrypt(data, CryptoJS.enc.Utf8.parse(secretKey), {
				iv: CryptoJS.enc.Utf8.parse(config.iv || ""),
				mode: CryptoJS.mode[config.mode || "ECB"],
				padding: CryptoJS.pad[config.padding || "Pkcs7"]
			})
			return result.toString()
		},
		decrypt(cipher, secretKey, config = {}) {
			const result = CryptoJS.AES.decrypt(cipher, CryptoJS.enc.Utf8.parse(secretKey), {
				iv: CryptoJS.enc.Utf8.parse(config.iv || ""),
				mode: CryptoJS.mode[config.mode || "ECB"],
				padding: CryptoJS.pad[config.padding || "Pkcs7"]
			})
			return CryptoJS.enc.Utf8.stringify(result);
		}
	}
}

tools.base64 = {
	encrypt(data) {
		return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(data))
	},
	decrypt(cipher) {
		return CryptoJS.enc.Base64.parse(cipher).toString(CryptoJS.enc.Utf8)
	}
}

// go语法捕获异常
tools.go = async function(fn) {
	try {
		let res = await fn
		return [res, null]
	} catch (err) {
		return [null, err]
	}
}

// 解析页面
tools.parsePage = function(page) {
	if (page.startsWith("tabbar=")) {
		uni.switchTab({
			url: page.slice(7),
			fail() {
				uni.showToast({
					icon: "none",
					title: "敬请启动"
				})
			}
		})
		return;
	}
	if (page.startsWith("page=")) {
		uni.navigateTo({
			url: page.slice(5),
			animationType: 'pop-in',
			animationDuration: 200,
			fail() {
				uni.showToast({
					icon: "none",
					title: "敬请启动"
				})
			}
		})
		return;
	}
}

export default tools;



// 检测小程序是否更新
export const VersionUpdate = () => {
	// 判断应用的 getUpdateManager 是否在当前版本可用
	console.log(uni.canIUse('getUpdateManager'));
	if (uni.canIUse('getUpdateManager')) {
		const updateManager = uni.getUpdateManager()
		// 向小程序后台请求完新版本信息
		updateManager.onCheckForUpdate(function(res) {
			if (res.hasUpdate) {
				//小程序有新版本，静默下载新版本，新版本下载完成
				updateManager.onUpdateReady(function() {
					//模态弹窗（确认、取消）
					uni.showModal({
						title: '更新提示',
						content: '小程序已发布新版本，是否重启？',
						success: function(res) {
							//用户点击确定
							if (res.confirm) {
								//当新版本下载完成，调用该方法会强制当前小程序应用上新版本并重启
								updateManager.applyUpdate()
							}
							//用户点击取消
							else if (res.cancel) {
								//强制用户更新，弹出第二次弹窗
								uni.showModal({
									title: '提示',
									content: '小程序已发布新版本，是否重启',
									showCancel: false, //隐藏取消按钮
									success: function(res) {
										//第二次提示后，强制更新
										if (res.confirm) {
											// 当新版本下载完成，调用该方法会强制当前小程序应用上新版本并重启
											updateManager.applyUpdate()
										} else if (res.cancel) {
											//重新回到版本更新提示
											VersionUpdate()
										}
									},
								})
							}
						},
					})
				})
				// 当新版本下载失败
				updateManager.onUpdateFailed(function() {
					uni.showModal({
						title: '提示',
						content: '请您删除当前小程序，重新打开小程序',
					})
				})
			}
		})
	} else {
		// 提示用户在最新版本的客户端上体验
		uni.showModal({
			title: '温馨提示',
			content: '当前微信版本过低，可能无法使用该功能，请升级到最新版本后重试。',
		})
	}
}