<template>
    <view class="verification-container">
        <!-- 顶部背景装饰 -->
        <view class="header-bg">
            <view class="bg-circle circle-1"></view>
            <view class="bg-circle circle-2"></view>
            <view class="bg-circle circle-3"></view>
        </view>

        <!-- 核销内容 -->
        <view class="verification-content">
            <!-- 核销卡片 -->
            <view class="verification-card">
                <!-- 卡片头部 -->
                <view class="card-header">
                    <view class="header-icon">
                        <text class="ri-qr-code-line"></text>
                    </view>
                    <view class="header-text">
                        <text class="title">团购核销</text>
                        <text class="subtitle">请输入您的核销码进行验证</text>
                    </view>
                </view>

                <!-- 核销码输入区域 -->
                <view class="input-section">
                    <view class="input-label">
                        <text class="label-text">核销码</text>
                        <text class="required">*</text>
                    </view>
                    <view class="input-container">
                        <input
                            v-model="verificationCode"
                            placeholder="请输入12位核销码"
                            maxlength="12"
                            class="code-input"
                            :class="{ 'error': inputError }"
                            @input="onInputChange"
                            @focus="clearError"
                        />
                        <view class="input-actions">
                            <text class="ri-close-circle-fill clear-btn" v-if="verificationCode" @click="clearInput"></text>
                            <text class="ri-qr-scan-2-line scan-btn" @click="scanCode"></text>
                        </view>
                    </view>
                    <view class="error-tip" v-if="inputError">
                        <text class="ri-error-warning-line"></text>
                        <text>{{ errorMessage }}</text>
                    </view>
                </view>

                <!-- 核销按钮 -->
                <view class="verify-section">
                    <button
                        class="verify-btn"
                        :class="{ 'disabled': !canVerify, 'loading': isVerifying }"
                        :disabled="!canVerify || isVerifying"
                        @click="handleVerification"
                    >
                        <text class="ri-loader-4-line loading-icon" v-if="isVerifying"></text>
                        <text>{{ isVerifying ? '核销中...' : '立即核销' }}</text>
                    </button>
                </view>

                <!-- 使用说明 -->
                <view class="usage-tips">
                    <view class="tips-header">
                        <text class="ri-information-line"></text>
                        <text>使用说明</text>
                    </view>
                    <view class="tips-list">
                        <text class="tip-item">• 核销码为12位数字或字母组合</text>
                        <text class="tip-item">• 每个核销码仅可使用一次</text>
                        <text class="tip-item">• 核销成功后不可撤销</text>
                        <text class="tip-item">• 如有问题请联系客服</text>
                    </view>
                </view>
            </view>

            <!-- 快捷操作 -->
            <view class="quick-actions">
                <view class="action-item" @click="scanCode">
                    <view class="action-icon">
                        <text class="ri-qr-scan-2-line"></text>
                    </view>
                    <text class="action-text">扫码核销</text>
                </view>
                <view class="action-item" @click="showHistory">
                    <view class="action-icon">
                        <text class="ri-history-line"></text>
                    </view>
                    <text class="action-text">核销记录</text>
                </view>
                <view class="action-item" @click="contactService">
                    <view class="action-icon">
                        <text class="ri-customer-service-2-line"></text>
                    </view>
                    <text class="action-text">联系客服</text>
                </view>
            </view>
        </view>

        <!-- 核销成功弹窗 -->
        <wd-popup v-model="showSuccessModal" position="center" :close-on-click-modal="false">
            <view class="success-modal">
                <view class="success-icon">
                    <text class="ri-checkbox-circle-fill"></text>
                </view>
                <text class="success-title">核销成功！</text>
                <view class="success-info">
                    <view class="info-item">
                        <text class="info-label">核销码：</text>
                        <text class="info-value">{{ successInfo.code }}</text>
                    </view>
                    <view class="info-item">
                        <text class="info-label">核销时间：</text>
                        <text class="info-value">{{ successInfo.time }}</text>
                    </view>
                    <view class="info-item">
                        <text class="info-label">套餐名称：</text>
                        <text class="info-value">{{ successInfo.packageName }}</text>
                    </view>
                </view>
                <button class="confirm-btn" @click="closeSuccessModal">确定</button>
            </view>
        </wd-popup>
    </view>
</template>

<script setup>
import { ref, computed } from 'vue'

// 数据状态
const verificationCode = ref('')
const inputError = ref(false)
const errorMessage = ref('')
const isVerifying = ref(false)
const showSuccessModal = ref(false)

// 成功信息
const successInfo = ref({
    code: '',
    time: '',
    packageName: ''
})

// 计算属性
const canVerify = computed(() => {
    return verificationCode.value.length >= 8 && !inputError.value
})

// 输入变化处理
const onInputChange = (e) => {
    const value = e.detail.value.toUpperCase()
    verificationCode.value = value

    // 实时验证格式
    if (value.length > 0 && !/^[A-Z0-9]+$/.test(value)) {
        inputError.value = true
        errorMessage.value = '核销码只能包含数字和字母'
    } else {
        inputError.value = false
        errorMessage.value = ''
    }
}

// 清除错误状态
const clearError = () => {
    inputError.value = false
    errorMessage.value = ''
}

// 清空输入
const clearInput = () => {
    verificationCode.value = ''
    clearError()
}

// 扫码核销
const scanCode = () => {
    uni.scanCode({
        success: (res) => {
            verificationCode.value = res.result
            handleVerification()
        },
        fail: (error) => {
            uni.showToast({
                title: '扫码失败',
                icon: 'error'
            })
        }
    })
}

// 处理核销
const handleVerification = async () => {
    if (!canVerify.value || isVerifying.value) return

    isVerifying.value = true

    try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 2000))

        // 模拟核销成功
        successInfo.value = {
            code: verificationCode.value,
            time: new Date().toLocaleString(),
            packageName: '陪驾体验套餐（2小时）'
        }

        showSuccessModal.value = true
        verificationCode.value = ''

    } catch (error) {
        inputError.value = true
        errorMessage.value = '核销失败，请检查核销码是否正确'

        uni.showToast({
            title: '核销失败',
            icon: 'error'
        })
    } finally {
        isVerifying.value = false
    }
}

// 关闭成功弹窗
const closeSuccessModal = () => {
    showSuccessModal.value = false
}

// 查看核销记录
const showHistory = () => {
    uni.navigateTo({
        url: '/pagesA/verification/history'
    })
}

// 联系客服
const contactService = () => {
    uni.showModal({
        title: '联系客服',
        content: '客服电话：400-123-4567\n工作时间：9:00-18:00',
        showCancel: false
    })
}
</script>

<style lang="scss" scoped>
page {
    background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
}

.verification-container {
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

/* 顶部背景装饰 */
.header-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 400rpx;
    background: linear-gradient(135deg, #FF9500 0%, #ff7b00 100%);
    overflow: hidden;
}

.bg-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
    width: 200rpx;
    height: 200rpx;
    top: -100rpx;
    right: -50rpx;
}

.circle-2 {
    width: 150rpx;
    height: 150rpx;
    top: 100rpx;
    left: -75rpx;
}

.circle-3 {
    width: 100rpx;
    height: 100rpx;
    top: 200rpx;
    right: 100rpx;
}

/* 内容区域 */
.verification-content {
    position: relative;
    z-index: 1;
    padding: 120rpx 32rpx 40rpx;
}

/* 核销卡片 */
.verification-card {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(248, 250, 255, 0.95) 100%);
    backdrop-filter: blur(20rpx);
    -webkit-backdrop-filter: blur(20rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    border-radius: 24rpx;
    padding: 40rpx;
    margin-bottom: 32rpx;
    box-shadow:
        0 20rpx 60rpx rgba(0, 0, 0, 0.1),
        0 8rpx 32rpx rgba(0, 0, 0, 0.05);
}

/* 卡片头部 */
.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 40rpx;
}

.header-icon {
    width: 80rpx;
    height: 80rpx;
    background: linear-gradient(135deg, #FF9500 0%, #ff7b00 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;
    box-shadow: 0 8rpx 24rpx rgba(255, 149, 0, 0.3);

    .ri-qr-code-line {
        font-size: 36rpx;
        color: #fff;
    }
}

.header-text {
    flex: 1;
}

.title {
    display: block;
    font-size: 36rpx;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 8rpx;
}

.subtitle {
    display: block;
    font-size: 26rpx;
    color: #666;
    line-height: 1.4;
}

/* 输入区域 */
.input-section {
    margin-bottom: 40rpx;
}

.input-label {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
}

.label-text {
    font-size: 28rpx;
    font-weight: 600;
    color: #1a1a1a;
}

.required {
    color: #ff4757;
    margin-left: 4rpx;
    font-size: 28rpx;
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.code-input {
    flex: 1;
    height: 96rpx;
    padding: 0 120rpx 0 24rpx;
    background: rgba(255, 255, 255, 0.8);
    border: 2rpx solid rgba(0, 0, 0, 0.08);
    border-radius: 16rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #1a1a1a;
    letter-spacing: 2rpx;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.code-input:focus {
    border-color: #FF9500;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 4rpx rgba(255, 149, 0, 0.1);
    outline: none;
}

.code-input.error {
    border-color: #ff4757;
    background: rgba(255, 71, 87, 0.05);
}

.input-actions {
    position: absolute;
    right: 16rpx;
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.clear-btn,
.scan-btn {
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    color: #999;
    transition: color 0.3s ease;
}

.clear-btn:active,
.scan-btn:active {
    color: #FF9500;
}

.error-tip {
    display: flex;
    align-items: center;
    margin-top: 12rpx;
    padding: 12rpx 16rpx;
    background: rgba(255, 71, 87, 0.05);
    border-radius: 12rpx;
    border-left: 4rpx solid #ff4757;

    .ri-error-warning-line {
        font-size: 24rpx;
        color: #ff4757;
        margin-right: 8rpx;
    }

    text {
        font-size: 24rpx;
        color: #ff4757;
    }
}

/* 核销按钮 */
.verify-section {
    margin-bottom: 40rpx;
}

.verify-btn {
    width: 100%;
    height: 96rpx;
    background: linear-gradient(135deg, #FF9500 0%, #ff7b00 100%);
    border: none;
    border-radius: 16rpx;
    font-size: 32rpx;
    font-weight: 700;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    transition: all 0.3s ease;
    box-shadow: 0 8rpx 24rpx rgba(255, 149, 0, 0.4);
}

.verify-btn:not(.disabled):not(.loading):active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 16rpx rgba(255, 149, 0, 0.5);
}

.verify-btn.disabled {
    background: #ccc;
    box-shadow: none;
    color: #999;
}

.verify-btn.loading {
    background: linear-gradient(135deg, #FF9500 0%, #ff7b00 100%);
    opacity: 0.8;
}

.loading-icon {
    font-size: 28rpx;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 使用说明 */
.usage-tips {
    background: rgba(248, 249, 250, 0.8);
    border-radius: 16rpx;
    padding: 24rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.tips-header {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    .ri-information-line {
        font-size: 28rpx;
        color: #FF9500;
        margin-right: 8rpx;
    }

    text {
        font-size: 26rpx;
        font-weight: 600;
        color: #1a1a1a;
    }
}

.tips-list {
    display: flex;
    flex-direction: column;
    gap: 8rpx;
}

.tip-item {
    font-size: 24rpx;
    color: #666;
    line-height: 1.5;
}

/* 快捷操作 */
.quick-actions {
    display: flex;
    justify-content: space-between;
    gap: 16rpx;
}

.action-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 32rpx 16rpx;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 16rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.action-item:active {
    background: rgba(255, 149, 0, 0.05);
    transform: translateY(2rpx);
}

.action-icon {
    width: 64rpx;
    height: 64rpx;
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12rpx;
    box-shadow: 0 4rpx 16rpx rgba(100, 116, 139, 0.2);

    text {
        font-size: 28rpx;
        color: #fff;
    }
}

.action-text {
    font-size: 24rpx;
    color: #666;
    font-weight: 500;
}

/* 成功弹窗 */
.success-modal {
    width: 560rpx;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(248, 250, 255, 0.95) 100%);
    backdrop-filter: blur(20rpx);
    -webkit-backdrop-filter: blur(20rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    border-radius: 24rpx;
    padding: 48rpx 40rpx;
    text-align: center;
    box-shadow:
        0 20rpx 60rpx rgba(0, 0, 0, 0.15),
        0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.success-icon {
    margin-bottom: 24rpx;

    .ri-checkbox-circle-fill {
        font-size: 120rpx;
        color: #22c55e;
    }
}

.success-title {
    display: block;
    font-size: 36rpx;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 32rpx;
}

.success-info {
    text-align: left;
    margin-bottom: 40rpx;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 0;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);

    &:last-child {
        border-bottom: none;
    }
}

.info-label {
    font-size: 26rpx;
    color: #666;
    font-weight: 500;
}

.info-value {
    font-size: 26rpx;
    color: #1a1a1a;
    font-weight: 600;
}

.confirm-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #FF9500 0%, #ff7b00 100%);
    border: none;
    border-radius: 16rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: #fff;
    transition: all 0.3s ease;
    box-shadow: 0 4rpx 16rpx rgba(255, 149, 0, 0.3);
}

.confirm-btn:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 8rpx rgba(255, 149, 0, 0.4);
}

/* 响应式调整 */
@media (max-width: 750rpx) {
    .verification-content {
        padding: 100rpx 24rpx 32rpx;
    }

    .verification-card {
        padding: 32rpx;
    }

    .quick-actions {
        flex-direction: column;
        gap: 12rpx;
    }

    .action-item {
        flex-direction: row;
        padding: 24rpx;

        .action-icon {
            width: 48rpx;
            height: 48rpx;
            margin-bottom: 0;
            margin-right: 16rpx;

            text {
                font-size: 24rpx;
            }
        }
    }

    .success-modal {
        width: 90vw;
        max-width: 500rpx;
        padding: 40rpx 32rpx;
    }
}
</style>