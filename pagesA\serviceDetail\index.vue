<template>
	<view class="service-detail-container">
		<!-- 轮播图区域 -->
		<view class="swiper-section">
			<swiper class="banner-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500"
				:circular="true" indicator-color="rgba(255, 255, 255, 0.4)" indicator-active-color="#ffffff"
				@change="handleSwiperChange">
				<swiper-item v-for="(item, index) in serviceDetail.bannerList" :key="index">
					<image class="banner-image" :src="item" mode="aspectFill" />
				</swiper-item>
			</swiper>
		</view>

		<!-- 服务信息区域 -->
		<view class="service-info-card">
			<view class="service-title">{{ serviceDetail.title }}</view>
			
			<view class="service-price-row">
				<view class="price-section">
					<text class="current-price">¥{{ serviceDetail.price }}</text>
					<text class="original-price">¥{{ serviceDetail.originalPrice }}</text>
				</view>
				<view class="enrollment">
					<text class="enroll-icon ri-user-3-line"></text>
					<text class="enroll-count">{{ serviceDetail.enrollCount }}人已报名</text>
				</view>
			</view>
		</view>



		<!-- 服务详情 -->
		<view class="detail-card">
			<view class="card-title">
				<view class="title-indicator"></view>
				<text>服务详情</text>
			</view>
			<view class="detail-content">
				<rich-text :nodes="serviceDetail.detailContent"></rich-text>
			</view>
		</view>

	

		<!-- 底部操作栏 -->
		<view class="bottom-action-bar safe-area-bottom">
			<view class="contact-btn" @click="handleContact">
				<text class="ri-customer-service-2-line"></text>
			</view>
			<view class="book-now-btn" @click="handleBookNow">立即下单</view>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';

// 当前轮播图索引
const currentSwiperIndex = ref(0);

// 服务详情数据
const serviceDetail = reactive({
	id: 1,
	title: '轿车陪驾2小时/SUV通用首单特惠驾驶体验课',
	price: '138',
	originalPrice: '168',
	enrollCount: 4339,
	bannerList: [
		'/static/images/1.png',
		'/static/images/2.png',
		'/static/images/1.png',
	],
	highlights: [
		{
			title: '专业陪驾师傅',
			desc: '持证上岗，拥有多年驾驶经验，耐心细致'
		},
		{
			title: '安全保障',
			desc: '全程购买保险，确保学员安全'
		},
		{
			title: '灵活时间',
			desc: '可根据个人时间安排课程，提前预约即可'
		},
		{
			title: '贴心服务',
			desc: '一对一指导，针对性解决驾驶难题'
		}
	],
	detailContent: `
		<div style="padding: 10px 0;">
			<p style="margin-bottom: 15px; line-height: 1.6; color: #333;">本课程适合刚拿到驾照的新手或需要提升驾驶技能的学员，由专业陪驾师傅一对一指导，帮助您快速掌握实际道路驾驶技巧。</p>
			
			<p style="margin-bottom: 15px; line-height: 1.6; color: #333;"><strong>课程内容包括：</strong></p>
			
			<ul style="padding-left: 20px; margin-bottom: 15px;">
				<li style="margin-bottom: 10px; line-height: 1.5;">基础操作复习与强化</li>
				<li style="margin-bottom: 10px; line-height: 1.5;">实际道路行驶技巧</li>
				<li style="margin-bottom: 10px; line-height: 1.5;">车辆控制与安全驾驶</li>
				<li style="margin-bottom: 10px; line-height: 1.5;">常见路况应对方法</li>
				<li style="margin-bottom: 10px; line-height: 1.5;">停车入库技巧指导</li>
			</ul>
			
			<p style="margin-bottom: 15px; line-height: 1.6; color: #333;"><strong>适合人群：</strong></p>
			
			<ul style="padding-left: 20px; margin-bottom: 15px;">
				<li style="margin-bottom: 10px; line-height: 1.5;">新手司机，刚拿到驾照</li>
				<li style="margin-bottom: 10px; line-height: 1.5;">长时间不开车需要恢复驾驶技能</li>
				<li style="margin-bottom: 10px; line-height: 1.5;">对驾驶缺乏信心需要提升</li>
				<li style="margin-bottom: 10px; line-height: 1.5;">需要熟悉特定路线或区域</li>
			</ul>
			
			<p style="line-height: 1.6; color: #333;">每节课2小时，可根据个人需求定制学习内容，确保学习效果。</p>
		</div>
	`,
	notices: [
		'预约成功后，客服会与您联系确认具体时间',
		'请至少提前24小时预约，以便安排合适的陪驾师傅',
		'如需取消预约，请提前12小时告知',
		'陪驾过程中请遵守交通规则，确保安全',
		'服务费用包含燃油费和过路费',
	]
});

// 监听轮播图变化
const handleSwiperChange = (e) => {
	currentSwiperIndex.value = e.detail.current;
};

// 联系客服
const handleContact = () => {
	uni.makePhoneCall({
		phoneNumber: '17729744485',
		success: () => {
			console.log('拨打电话成功');
		},
		fail: (err) => {
			console.error('拨打电话失败', err);
		}
	});
};

// 立即预约
const handleBookNow = () => {
	uni.navigateTo({
		url: '/pagesA/booking/index?serviceId=' + serviceDetail.id + '&serviceName=' + encodeURIComponent(serviceDetail.title) + '&price=' + serviceDetail.price
	});
};

// 页面加载
onMounted(() => {

});
</script>

<style lang="scss">
page {
	background: linear-gradient(180deg, #eef2ff 0%, #f7f9ff 40%, #eef2ff 100%);
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.service-detail-container {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	padding-bottom: calc(100rpx + env(safe-area-inset-bottom)); /* 为底部操作栏留出空间 */
	position: relative;
}

.service-detail-container::before {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	width: 300rpx;
	height: 300rpx;
	background-image: radial-gradient(circle, rgba(255, 149, 0, 0.08) 20%, transparent 70%);
	z-index: -1;
}

.service-detail-container::after {
	content: '';
	position: absolute;
	bottom: 200rpx;
	left: -100rpx;
	width: 400rpx;
	height: 400rpx;
	background-image: radial-gradient(circle, rgba(64, 158, 255, 0.06) 30%, transparent 70%);
	z-index: -1;
	border-radius: 50%;
}

/* 轮播图区域 */
.swiper-section {
	position: relative;
	width: 100%;
	height: 500rpx;
}

.banner-swiper {
	width: 100%;
	height: 100%;
}

.banner-image {
	width: 100%;
	height: 100%;
}

.swiper-indicator {
	position: absolute;
	right: 30rpx;
	bottom: 30rpx;
	background-color: rgba(0, 0, 0, 0.5);
	color: #ffffff;
	font-size: 24rpx;
	padding: 6rpx 16rpx;
	border-radius: 20rpx;
	z-index: 10;
}

/* 服务信息卡片 */
.service-info-card {
	margin: 20rpx;
	padding: 30rpx;
	background-color: #ffffff;
	border-radius: 16rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.service-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	line-height: 1.4;
	margin-bottom: 20rpx;
}

.service-price-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.price-section {
	display: flex;
	align-items: baseline;
}

.current-price {
	font-size: 40rpx;
	font-weight: bold;
	color: #F05F3A;
	margin-right: 16rpx;
}

.original-price {
	font-size: 28rpx;
	color: #999;
	text-decoration: line-through;
}

.enrollment {
	display: flex;
	align-items: center;
}

.enroll-icon {
	font-size: 24rpx;
	color: #999;
	margin-right: 6rpx;
}

.enroll-count {
	font-size: 24rpx;
	color: #999;
}

/* 服务亮点卡片 */
.highlights-card,
.detail-card,
.notice-card {
	margin: 20rpx;
	padding: 30rpx;
	background-color: #ffffff;
	border-radius: 16rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-title {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.title-indicator {
	width: 6rpx;
	height: 32rpx;
	background-color: #FF9500;
	margin-right: 16rpx;
	border-radius: 3rpx;
}

.highlights-list {
	margin-top: 20rpx;
}

.highlight-item {
	display: flex;
	margin-bottom: 24rpx;
}

.highlight-item:last-child {
	margin-bottom: 0;
}

.highlight-number {
	width: 40rpx;
	height: 40rpx;
	background-color: #FF9500;
	color: #ffffff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.highlight-content {
	flex: 1;
}

.highlight-title {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 8rpx;
}

.highlight-desc {
	font-size: 24rpx;
	color: #666;
	line-height: 1.5;
}

/* 服务详情 */
.detail-content {
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
}

/* 服务须知 */
.notice-list {
	margin-top: 16rpx;
}

.notice-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 16rpx;
}

.notice-icon {
	font-size: 28rpx;
	color: #FF9500;
	margin-right: 12rpx;
	margin-top: 4rpx;
}

.notice-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	flex: 1;
}

/* 底部操作栏 */
.bottom-action-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 80rpx;
	background-color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 20rpx 0;
	padding-bottom: env(safe-area-inset-bottom);
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	z-index: 100;
}

.safe-area-bottom {
	padding-bottom: env(safe-area-inset-bottom);
}

.contact-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 80rpx;
	height: 80rpx;
	border-right: 1px solid #eee;
}

.contact-btn text {
	font-size: 44rpx;
	color: #666;
}

.book-now-btn {
	background: linear-gradient(to right, #FF9500, #FF7D00);
	color: #ffffff;
	font-size: 30rpx;
	font-weight: 500;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 60rpx;
	flex: 1;
	margin-left: 20rpx;
	box-shadow: 0 4rpx 8rpx rgba(255, 149, 0, 0.2);
}
</style> 