<template>
    <view class="panel-container">
        <!-- 顶部状态栏 -->
        <view class="status-header">
            <view class="coach-info">
                <image class="coach-avatar" :src="coachInfo.avatar" mode="aspectFill" />
                <view class="coach-details">
                    <text class="coach-name">{{ coachInfo.name }}</text>
                    <view class="status-badge" :class="coachInfo.status">
                        <text class="status-dot"></text>
                        <text class="status-text">{{ getStatusText(coachInfo.status) }}</text>
                    </view>
                </view> 
            </view>
            <view class="service-switch">
                <text class="switch-label">接单状态</text>
                <switch
                    :checked="isOnline"
                    @change="toggleOnlineStatus"
                    color="#FF9500"
                    class="status-switch"
                />
            </view>
        </view>

        <!-- 今日数据概览 -->
        <view class="data-overview">
            <view class="overview-header">
                <view class="header-left">
                    <text class="section-title">今日数据</text>
                    <text class="current-time">{{ currentTime }}</text>
                </view>
                <text class="date-text">{{ todayDate }}</text>
            </view>
            <view class="data-grid">
                <view class="data-item" @click="viewOrderDetails">
                    <view class="data-icon">
                        <text class="ri-file-list-3-line"></text>
                    </view>
                    <text class="data-value">{{ todayStats.orders }}</text>
                    <text class="data-label">接单数</text>
                </view>
                <view class="data-item" @click="viewEarnings">
                    <view class="data-icon">
                        <text class="ri-money-cny-circle-line"></text>
                    </view>
                    <text class="data-value">¥{{ todayStats.income }}</text>
                    <text class="data-label">收入</text>
                </view>
                <view class="data-item" @click="viewSchedule">
                    <view class="data-icon">
                        <text class="ri-time-line"></text>
                    </view>
                    <text class="data-value">{{ todayStats.hours }}h</text>
                    <text class="data-label">服务时长</text>
                </view>
                <view class="data-item" @click="viewReviews">
                    <view class="data-icon">
                        <text class="ri-star-line"></text>
                    </view>
                    <text class="data-value">{{ todayStats.rating }}</text>
                    <text class="data-label">评分</text>
                </view>
            </view>
        </view>

        <!-- 订单管理 -->
        <view class="order-section">
            <view class="section-header">
                <text class="section-title">订单管理</text>
                <text class="more-btn" @click="viewAllOrders">查看全部</text>
            </view>
            
            <!-- 当前订单 -->
            <view class="current-order" v-if="currentOrder">
                <view class="order-header">
                    <view class="order-status" :class="currentOrder.status">
                        {{ getOrderStatusText(currentOrder.status) }}
                    </view>
                    <text class="order-time">{{ currentOrder.time }}</text>
                </view>
                <view class="order-content">
                    <view class="customer-info">
                        <image class="customer-avatar" :src="currentOrder.customer.avatar" />
                        <view class="customer-details">
                            <text class="customer-name">{{ currentOrder.customer.name }}</text>
                            <text class="service-type">{{ currentOrder.serviceType }}</text>
                        </view>
                    </view>
                    <view class="order-actions">
                        <button class="action-btn contact" @click="contactCustomer">联系</button>
                        <button class="action-btn primary" @click="handleOrderAction">
                            {{ getActionText(currentOrder.status) }}
                        </button>
                    </view>
                </view>
            </view>

            <!-- 无当前订单 -->
            <view class="no-order" v-else>
                <view class="no-order-icon">
                    <text class="ri-calendar-check-line"></text>
                </view>
                <text class="no-order-text">暂无进行中的订单</text>
                <text class="no-order-tip">开启接单状态，等待新订单</text>
            </view>
        </view>

        <!-- 快捷功能 -->
        <view class="quick-functions">
            <view class="function-grid">
                <view class="function-item" @click="viewSchedule">
                    <view class="function-icon schedule">
                        <text class="ri-calendar-line"></text>
                    </view>
                    <text class="function-text">我的日程</text>
                </view>
                <view class="function-item" @click="viewEarnings">
                    <view class="function-icon earnings">
                        <text class="ri-money-dollar-circle-line"></text>
                    </view>
                    <text class="function-text">收入明细</text>
                </view>
                <view class="function-item" @click="viewReviews">
                    <view class="function-icon reviews">
                        <text class="ri-star-line"></text>
                    </view>
                    <text class="function-text">评价管理</text>
                </view>
                <view class="function-item" @click="viewSettings">
                    <view class="function-icon settings">
                        <text class="ri-settings-3-line"></text>
                    </view>
                    <text class="function-text">设置</text>
                </view>
            </view>
        </view>

        <!-- 底部统计 -->
        <view class="bottom-stats">
            <view class="stats-item">
                <text class="stats-label">本月收入</text>
                <text class="stats-value">¥{{ monthlyStats.income }}</text>
            </view>
            <view class="stats-item">
                <text class="stats-label">服务评分</text>
                <text class="stats-value">{{ monthlyStats.rating }}/5.0</text>
            </view>
            <view class="stats-item">
                <text class="stats-label">完成订单</text>
                <text class="stats-value">{{ monthlyStats.orders }}单</text>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 教练信息
const coachInfo = ref({
    name: '张教练',
    avatar: '/static/images/coach-avatar.png',
    status: 'online' // online, offline, busy
})

// 在线状态
const isOnline = ref(true)

// 今日数据
const todayStats = ref({
    orders: 8,
    income: 680,
    hours: 6.5,
    rating: 4.9
})

// 月度统计
const monthlyStats = ref({
    income: 15680,
    rating: 4.8,
    orders: 156
})

// 当前订单
const currentOrder = ref({
    id: 'ORD20241201001',
    status: 'ongoing', // pending, ongoing, completed
    time: '14:30-16:30',
    customer: {
        name: '李同学',
        avatar: '/static/images/customer-avatar.png'
    },
    serviceType: '陪驾练习（2小时）'
})

// 当前时间
const currentTime = ref('')

// 今日日期
const todayDate = computed(() => {
    const today = new Date()
    return `${today.getMonth() + 1}月${today.getDate()}日`
})

// 更新时间
const updateTime = () => {
    const now = new Date()
    const hours = now.getHours().toString().padStart(2, '0')
    const minutes = now.getMinutes().toString().padStart(2, '0')
    currentTime.value = `${hours}:${minutes}`
}

// 定时更新时间
let timeInterval = null

// 获取状态文本
const getStatusText = (status) => {
    const statusMap = {
        online: '在线',
        offline: '离线',
        busy: '忙碌中'
    }
    return statusMap[status] || '未知'
}

// 获取订单状态文本
const getOrderStatusText = (status) => {
    const statusMap = {
        pending: '待开始',
        ongoing: '进行中',
        completed: '已完成'
    }
    return statusMap[status] || '未知'
}

// 获取操作按钮文本
const getActionText = (status) => {
    const actionMap = {
        pending: '开始服务',
        ongoing: '完成订单',
        completed: '查看详情'
    }
    return actionMap[status] || '操作'
}

// 切换在线状态
const toggleOnlineStatus = (e) => {
    isOnline.value = e.detail.value
    coachInfo.value.status = e.detail.value ? 'online' : 'offline'
    
    uni.showToast({
        title: e.detail.value ? '已开启接单' : '已关闭接单',
        icon: 'success'
    })
}

// 联系客户
const contactCustomer = () => {
    uni.showActionSheet({
        itemList: ['拨打电话', '发送短信'],
        success: (res) => {
            if (res.tapIndex === 0) {
                uni.makePhoneCall({
                    phoneNumber: '13800138000'
                })
            }
        }
    })
}

// 处理订单操作
const handleOrderAction = () => {
    const status = currentOrder.value.status
    if (status === 'pending') {
        currentOrder.value.status = 'ongoing'
        uni.showToast({ title: '服务已开始', icon: 'success' })
    } else if (status === 'ongoing') {
        currentOrder.value.status = 'completed'
        uni.showToast({ title: '订单已完成', icon: 'success' })
        // 可以跳转到评价页面
    }
}

// 查看所有订单
const viewAllOrders = () => {
    uni.navigateTo({
        url: '/pagesB/orders/list'
    })
}

// 查看订单详情
const viewOrderDetails = () => {
    uni.navigateTo({
        url: '/pagesB/orders/list?tab=today'
    })
}

// 查看日程
const viewSchedule = () => {
    uni.navigateTo({
        url: '/pagesB/schedule/index'
    })
}

// 查看收入
const viewEarnings = () => {
    uni.navigateTo({
        url: '/pagesB/earnings/index'
    })
}

// 查看评价
const viewReviews = () => {
    uni.navigateTo({
        url: '/pagesB/reviews/index'
    })
}

// 设置
const viewSettings = () => {
    uni.navigateTo({
        url: '/pagesB/settings/index'
    })
}

onMounted(() => {
    // 页面加载时获取最新数据
    loadCoachData()
    // 初始化时间
    updateTime()
    // 每分钟更新一次时间
    timeInterval = setInterval(updateTime, 60000)
})

// 页面卸载时清理定时器
onUnmounted(() => {
    if (timeInterval) {
        clearInterval(timeInterval)
    }
})

const loadCoachData = async () => {
    // 这里调用API获取教练数据
    console.log('加载教练数据')
}
</script>

<style lang="scss" scoped>
page {
    background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
}

.panel-container {
    min-height: 100vh;
    padding: 20rpx;
}

/* 顶部状态栏 */
.status-header {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.95) 0%, 
        rgba(248, 250, 255, 0.95) 100%);
    backdrop-filter: blur(20rpx);
    border-radius: 20rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.coach-info {
    display: flex;
    align-items: center;
}

.coach-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: 20rpx;
    border: 3rpx solid #fff;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.coach-details {
    display: flex;
    flex-direction: column;
    gap: 8rpx;
}

.coach-name {
    font-size: 32rpx;
    font-weight: 700;
    color: #1a1a1a;
}

.status-badge {
    display: flex;
    align-items: center;
    gap: 8rpx;
    
    &.online .status-dot {
        background: #22c55e;
    }
    
    &.offline .status-dot {
        background: #94a3b8;
    }
    
    &.busy .status-dot {
        background: #f59e0b;
    }
}

.status-dot {
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
}

.status-text {
    font-size: 24rpx;
    color: #666;
}

.service-switch {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
}

.switch-label {
    font-size: 24rpx;
    color: #666;
}

/* 数据概览 */
.data-overview {
    background: linear-gradient(135deg, #FF9500 0%, #ff7b00 100%);
    border-radius: 20rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 8rpx 32rpx rgba(255, 149, 0, 0.3);
}

.overview-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24rpx;
}

.header-left {
    display: flex;
    flex-direction: column;
    gap: 4rpx;
}

.current-time {
    font-size: 20rpx;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 400;
}

.section-title {
    font-size: 32rpx;
    font-weight: 700;
    color: #fff;
}

.date-text {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
}

.data-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24rpx;
}

.data-item {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10rpx);
    border-radius: 16rpx;
    padding: 20rpx;
    text-align: center;
    border: 1rpx solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    cursor: pointer;
}

.data-item:active {
    transform: translateY(2rpx);
    background: rgba(255, 255, 255, 0.2);
}

.data-icon {
    width: 32rpx;
    height: 32rpx;
    margin: 0 auto 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.data-icon text {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.9);
}

.data-value {
    display: block;
    font-size: 36rpx;
    font-weight: 700;
    color: #fff;
    margin-bottom: 8rpx;
}

.data-label {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
}

/* 订单管理 */
.order-section {
    background: #fff;
    border-radius: 20rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
}

.section-title {
    font-size: 32rpx;
    font-weight: 700;
    color: #1a1a1a;
}

.more-btn {
    font-size: 26rpx;
    color: #FF9500;
    font-weight: 500;
}

.current-order {
    border: 2rpx solid #f0f0f0;
    border-radius: 16rpx;
    padding: 24rpx;
    background: #fafafa;
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
}

.order-status {
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-size: 22rpx;
    font-weight: 600;

    &.pending {
        background: rgba(249, 115, 22, 0.1);
        color: #f97316;
    }

    &.ongoing {
        background: rgba(34, 197, 94, 0.1);
        color: #22c55e;
    }

    &.completed {
        background: rgba(100, 116, 139, 0.1);
        color: #64748b;
    }
}

.order-time {
    font-size: 24rpx;
    color: #666;
}

.order-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.customer-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.customer-avatar {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    margin-right: 16rpx;
}

.customer-details {
    display: flex;
    flex-direction: column;
    gap: 4rpx;
}

.customer-name {
    font-size: 28rpx;
    font-weight: 600;
    color: #1a1a1a;
}

.service-type {
    font-size: 24rpx;
    color: #666;
}

.order-actions {
    display: flex;
    gap: 12rpx;
}

.action-btn {
    padding: 12rpx 20rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
    font-weight: 500;
    border: none;

    &.contact {
        background: #f8f9fa;
        color: #666;
        border: 1rpx solid #e9ecef;
    }

    &.primary {
        background: linear-gradient(135deg, #FF9500 0%, #ff7b00 100%);
        color: #fff;
        box-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.3);
    }
}

.no-order {
    text-align: center;
    padding: 60rpx 0;
}

.no-order-icon {
    width: 120rpx;
    height: 120rpx;
    background: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20rpx;

    .ri-calendar-check-line {
        font-size: 60rpx;
        color: #ccc;
    }
}

.no-order-text {
    display: block;
    font-size: 28rpx;
    color: #666;
    margin-bottom: 8rpx;
}

.no-order-tip {
    font-size: 24rpx;
    color: #999;
}

/* 快捷功能 */
.quick-functions {
    background: #fff;
    border-radius: 20rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.function-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 32rpx;
}

.function-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12rpx;
    transition: transform 0.3s ease;
}

.function-item:active {
    transform: translateY(2rpx);
}

.function-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    text {
        font-size: 36rpx;
        color: #fff;
    }

    &.schedule {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    }

    &.earnings {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }

    &.reviews {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }

    &.settings {
        background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    }
}

.function-text {
    font-size: 24rpx;
    color: #666;
    font-weight: 500;
    text-align: center;
}

/* 底部统计 */
.bottom-stats {
    background: #fff;
    border-radius: 20rpx;
    padding: 32rpx;
    display: flex;
    justify-content: space-between;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.stats-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
    flex: 1;
}

.stats-label {
    font-size: 24rpx;
    color: #666;
}

.stats-value {
    font-size: 32rpx;
    font-weight: 700;
    color: #1a1a1a;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
    .panel-container {
        padding: 16rpx;
    }

    .status-header {
        padding: 24rpx;
        flex-direction: column;
        gap: 20rpx;
        align-items: flex-start;
    }

    .data-grid {
        grid-template-columns: 1fr 1fr;
        gap: 16rpx;
    }

    .function-grid {
        grid-template-columns: 1fr 1fr;
        gap: 24rpx;
    }

    .function-icon {
        width: 64rpx;
        height: 64rpx;

        text {
            font-size: 28rpx;
        }
    }

    .bottom-stats {
        flex-direction: column;
        gap: 20rpx;
    }

    .stats-item {
        flex-direction: row;
        justify-content: space-between;
        padding: 16rpx 0;
        border-bottom: 1rpx solid #f0f0f0;

        &:last-child {
            border-bottom: none;
        }
    }
}

</style>
