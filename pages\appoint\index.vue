<template>
	<view class="appoint-container">
		<!-- 顶部导航栏 -->
		<wd-navbar title="指定师傅陪驾" safeAreaInsetTop fixed placeholder :show-cancel-button="false"
			background="transparent" title-color="#333333" />

		<!-- 固定区域 -->
		<view class="fixed-header">
			<!-- 搜索框 -->
			<view class="search-box">
				<text class="ri-search-line search-icon"></text>
				<input class="search-input" type="text" placeholder="可以输入师傅姓名进行搜索" confirm-type="search"
					v-model="searchText" @confirm="handleSearch" />
			</view>

			<!-- 筛选区域 -->
			<view class="filter-area">
				<wd-drop-menu class="filter-drop-menu">
					<wd-drop-menu-item
						v-model="selectedAreaValue"
						:options="areaOptions"
						@change="handleAreaChange"
						:title="selectedAreaName"
					/>
					<wd-drop-menu-item
						v-model="selectedCarTypeValue"
						:options="carTypeOptions"
						@change="handleCarTypeChange"
						:title="selectedCarTypeName"
					/>
					<wd-drop-menu-item
						v-model="selectedPowerTypeValue"
						:options="powerTypeOptions"
						@change="handlePowerTypeChange"
						:title="selectedPowerTypeName"
					/>
				</wd-drop-menu>
			</view>
		</view>

		<!-- 内容区域（添加顶部边距，为固定区域留出空间） -->
		<view class="content-area">
			<!-- 师傅列表 -->
			<view class="coach-list">
				<view class="coach-item" v-for="(item, index) in filteredCoachList" :key="index"
					@click="handleCoachSelect(item)">
					<view class="coach-avatar">
						<image :src="item.avatar" mode="aspectFill"></image>
						<view class="coach-badge">新手专送</view>
					</view>
					<view class="coach-info">
						<view class="coach-header">
							<view class="coach-name">{{ item.name }}</view>
							<view class="book-btn" >较忙碌</view>
						</view>
						<view class="coach-tags">
							<text v-for="(tag, tIndex) in item.tags" :key="tIndex">{{ tag }}</text>
						</view>
						<view class="coach-areas">{{ item.areas }}</view>
						<view class="coach-service">
							<text>已服务次数</text>
							<text class="service-count">{{ item.serviceCount }}</text>
						</view>
					</view>
				</view>

				<!-- 底部提示 -->
				<view class="bottom-tip">
					<text class="tip-text">—— 已经到底啦 ——</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed } from 'vue';

// 搜索文本
const searchText = ref('');

// 筛选数据 - DropMenu 格式
const selectedAreaValue = ref('all')
const selectedCarTypeValue = ref('all')
const selectedPowerTypeValue = ref('all')

// 筛选选项数据 - DropMenu 格式
const areaOptions = ref([
	{ label: '服务区域', value: 'all' },
	{ label: '上城区', value: 'shangcheng' },
	{ label: '临平区', value: 'linping' },
	{ label: '江干区', value: 'jianggan' },
	{ label: '滨江区', value: 'binjiang' },
	{ label: '钱塘区', value: 'qiantang' },
	{ label: '萧山区', value: 'xiaoshan' },
	{ label: '西湖区', value: 'xihu' },
	{ label: '余杭区', value: 'yuhang' }
]);

const carTypeOptions = ref([
	{ label: '车型', value: 'all' },
	{ label: '轿车', value: 'sedan' },
	{ label: 'SUV', value: 'suv' }
]);

const powerTypeOptions = ref([
	{ label: '动力类型', value: 'all' },
	{ label: '燃油车', value: 'gas' },
	{ label: '新能源电车', value: 'electric' }
]);

// 计算当前选中的筛选名称
const selectedAreaName = computed(() => {
	const area = areaOptions.value.find(item => item.value === selectedAreaValue.value)
	return area ? area.label : '服务区域'
})

const selectedCarTypeName = computed(() => {
	const carType = carTypeOptions.value.find(item => item.value === selectedCarTypeValue.value)
	return carType ? carType.label : '车型'
})

const selectedPowerTypeName = computed(() => {
	const powerType = powerTypeOptions.value.find(item => item.value === selectedPowerTypeValue.value)
	return powerType ? powerType.label : '动力类型'
})

// 师傅列表数据
const coachList = ref([{
	id: 1,
	name: '施悦明',
	avatar: '/static/images/2.png',
	tags: ['轿车', '新能源电车', '比亚迪秦'],
	areas: '上城区,临平区,江干区,滨江区,钱塘区',
	serviceCount: '788',
	isPopular: true,
	isBusy: true
}, {
	id: 2,
	name: '李贤旺',
	avatar: '/static/images/2.png',
	tags: ['SUV', '新能源电车', '北汽EX360'],
	areas: '滨江区,萧山区,西湖区',
	serviceCount: '636',
	isPopular: true,
	isBusy: true
}, {
	id: 3,
	name: '姜敏',
	avatar: '/static/images/2.png',
	tags: ['SUV', '燃油车', '奇瑞/瑞虎5X/自动挡/1.5T'],
	areas: '上城区,临平区,余杭区,拱墅区,滨江区,萧山区,西湖区,钱塘区',
	serviceCount: '631',
	isPopular: false,
	isBusy: true
}, {
	id: 4,
	name: '种立光',
	avatar: '/static/images/2.png',
	tags: ['轿车', '燃油车', '现代', '轿车', '新能源电车', '北京现代'],
	areas: '上城区,临平区,余杭区',
	serviceCount: '598',
	isPopular: true,
	isBusy: false
}]);

// 根据筛选条件过滤师傅列表
const filteredCoachList = computed(() => {
	return coachList.value.filter(coach => {
		// 搜索过滤
		if (searchText.value && !coach.name.includes(searchText.value)) {
			return false;
		}

		// 区域过滤 - 使用新的变量名和值
		if (selectedAreaValue.value && selectedAreaValue.value !== 'all') {
			const selectedAreaLabel = areaOptions.value.find(item => item.value === selectedAreaValue.value)?.label;
			if (selectedAreaLabel && !coach.areas.includes(selectedAreaLabel)) {
				return false;
			}
		}

		// 车型过滤 - 使用新的变量名和值
		if (selectedCarTypeValue.value && selectedCarTypeValue.value !== 'all') {
			const selectedCarTypeLabel = carTypeOptions.value.find(item => item.value === selectedCarTypeValue.value)?.label;
			if (selectedCarTypeLabel && !coach.tags.some(tag => tag.includes(selectedCarTypeLabel))) {
				return false;
			}
		}

		// 动力类型过滤 - 使用新的变量名和值
		if (selectedPowerTypeValue.value && selectedPowerTypeValue.value !== 'all') {
			const selectedPowerTypeLabel = powerTypeOptions.value.find(item => item.value === selectedPowerTypeValue.value)?.label;
			if (selectedPowerTypeLabel && !coach.tags.some(tag => tag.includes(selectedPowerTypeLabel))) {
				return false;
			}
		}

		return true;
	});
});

// 处理筛选变化事件
const handleAreaChange = ({ value, selectedItem }) => {
	console.log('选中的区域:', selectedItem)
	// 这里可以添加筛选逻辑
};

const handleCarTypeChange = ({ value, selectedItem }) => {
	console.log('选中的车型:', selectedItem)
	// 这里可以添加筛选逻辑
};

const handlePowerTypeChange = ({ value, selectedItem }) => {
	console.log('选中的动力类型:', selectedItem)
	// 这里可以添加筛选逻辑
};

// 处理搜索
const handleSearch = () => {
	// 搜索逻辑
	console.log('搜索:', searchText.value);
};

// 处理师傅选择
const handleCoachSelect = (item) => {
	uni.navigateTo({
		url: `/pagesA/reservation/index?id=${item.id}`
	});
};



</script>

<style lang="scss">
page {
	background: linear-gradient(180deg, #f5f7fd 0%, #fafbff 40%, #ffffff 100%);
	--primary-color: #FF9500;
	--card-bg: #ffffff;
	--list-bg: #f9faff;
	--text-main: #333333;
	--text-secondary: #666666;
	--text-light: #999999;
	--price-color: #FF9500;
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.appoint-container {
	min-height: 100vh;
	position: relative;
	padding-bottom: 120rpx;
}

/* 固定区域 */
.fixed-header {
	position: fixed;
	/* #ifdef MP-WEIXIN */
	top: 0; /* 修改为0，紧贴导航栏 */
	padding-top: 180rpx; /* 使用padding代替margin，避免空隙 */
	/* #endif */
	left: 0;
	right: 0;
	z-index: 100;
	background: #fff;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
}

/* 内容区域 */
.content-area {
	margin-top: 220rpx; /* 进一步减少空隙 */
	background: #f5f7fd;
}

/* 搜索框 */
.search-box {
	margin: 20rpx 24rpx;
	height: 70rpx;
	background-color: #f5f7fd;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	padding: 0 40rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
}

.search-icon {
	font-size: 40rpx;
	color: #999;
	margin-right: 12rpx;
}

.search-input {
	flex: 1;
	height: 80rpx;
	font-size: 28rpx;
}

/* 筛选区域 */
.filter-area {
	padding: 0 24rpx 20rpx;
	background-color: #fff;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
}

/* DropMenu 样式 */
.filter-drop-menu {
	width: 100%;
}

:deep(.wd-drop-menu) {
	width: 100%;
	height: 80rpx;
	line-height: 80rpx;
	background-color: #fff;
	border-radius: 0;
	border: none;
}

:deep(.wd-drop-menu__item) {
	flex: 1;
	text-align: center;
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	height: 80rpx;
	line-height: 80rpx;
}

:deep(.wd-drop-menu__item-title) {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
}

:deep(.wd-drop-menu__item-title-text) {
	max-width: 120rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

:deep(.wd-drop-menu__item.active) {
	color: #FF9500;
}

:deep(.wd-drop-menu__item.active .wd-drop-menu__arrow) {
	color: #FF9500;
}

:deep(.wd-drop-menu__arrow) {
	font-size: 24rpx;
	margin-left: 8rpx;
}

/* 师傅列表 */
.coach-list {
	padding: 20rpx 24rpx 0;
}

.coach-item {
	display: flex;
	background-color: #fff;
	border-radius: 12rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
	margin-bottom: 24rpx;
	position: relative;
}

.coach-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	overflow: hidden;
	margin-right: 24rpx;
	position: relative;
}

.coach-avatar image {
	width: 100%;
	height: 100%;
}

.coach-badge {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: rgba(255, 149, 0, 0.8);
	color: #ffffff;
	font-size: 20rpx;
	text-align: center;
	padding: 4rpx 0;
}

.coach-info {
	flex: 1;
}

.coach-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}

.coach-name {
	font-size: 32rpx;
	font-weight: 500;
	color: var(--text-main);
}

.book-btn {
	background: #ff8500;
	color: #ffffff;
	font-size: 24rpx;
	padding: 8rpx 24rpx;
	border-radius: 30rpx;
	text-align: center;
	font-weight: 500;
	box-shadow: 0 4rpx 8rpx rgba(255, 133, 0, 0.2);
}

.coach-tags {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 12rpx;
}

.coach-tags text {
	font-size: 24rpx;
	color: var(--text-secondary);
	background-color: #f5f7fd;
	padding: 4rpx 16rpx;
	border-radius: 20rpx;
	margin-right: 16rpx;
	margin-bottom: 8rpx;
}

.coach-areas {
	font-size: 24rpx;
	color: var(--text-secondary);
	margin-bottom: 12rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	overflow: hidden;
}

.coach-service {
	display: flex;
	align-items: center;
	font-size: 24rpx;
	color: var(--text-light);
}

.service-count {
	color: var(--primary-color);
	font-weight: 500;
	margin-left: 8rpx;
}

/* 底部提示 */
.bottom-tip {
	text-align: center;
	padding: 40rpx 0;
}

.tip-text {
	font-size: 24rpx;
	color: var(--text-light);
}

/* 通用样式 */
.li-mr-3 {
	margin-right: 12rpx;
}

.li-ml-2 {
	margin-left: 8rpx;
}

.li-text-40 {
	font-size: 40rpx;
}

.li-text-gray-400 {
	color: #9ca3af;
}
</style>