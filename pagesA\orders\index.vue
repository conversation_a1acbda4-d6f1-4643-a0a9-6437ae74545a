<template>
	<view class="orders-page">
		<!-- 订单状态筛选 -->
		<view class="tabs-container">
			<wd-tabs v-model="activeTabIndex" @change="handleTabChange" sticky offset-top="88rpx" line-width="50rpx"
				line-height="4rpx" color="#FF9500" inactive-color="#999">
				<wd-tab
					v-for="(item, index) in orderStatusList"
					:key="index"
					:title="item.name"
					:name="index"
					:badge-props="item.count > 0 ? {
						modelValue: item.count,
						max: 99,
						right: '-8px'
					} : null"
				></wd-tab>
			</wd-tabs>
		</view>

		<!-- 订单列表 -->
		<view class="orders-container">
			<!-- 加载状态 -->
			<view v-if="loading" class="loading-container">
				<wd-loading type="outline" color="#FF9500" />
				<text class="loading-text">加载中...</text>
			</view>

			<!-- 订单列表 -->
			<view v-else-if="filteredOrderList.length > 0" class="order-list">
				<view v-for="(order, index) in filteredOrderList" :key="order.id" class="order-card"
					@click="viewOrderDetail(order)">
					<!-- 订单主体内容 -->
					<view class="order-content">
						<!-- 教练信息区域 -->
						<view class="coach-section">
							<image class="coach-avatar" :src="order.coachAvatar || defaultAvatar" mode="aspectFill"
								@error="handleImageError"></image>
							<view class="coach-info">
								<view class="coach-name">{{ order.coachName }}</view>
								<view class="coach-rating">
									<text class="rating-stars">★★★★★</text>
									<text class="rating-score">4.9</text>
								</view>
							</view>
							<view class="price-display">
								<view class="status-indicator" :class="getStatusClass(order.status)">
									<text class="status-icon" :class="getStatusIcon(order.status)"></text>
									<text class="status-text">{{ getStatusText(order.status) }}</text>
								</view>
								<view class="price-info">
									<text class="price-symbol">¥</text>
									<text class="price-number">{{ order.actualAmount }}</text>
								</view>
							</view>
						</view>

						<!-- 陪驾详情区域 -->
						<view class="driving-details">
							<view class="detail-row">
								<view class="detail-item">
									<text class="ri-map-pin-line detail-icon"></text>
									<text class="detail-text">{{ order.startLocation }}</text>
								</view>
							</view>
							<view class="detail-row">
								<view class="detail-item">
									<text class="ri-time-line detail-icon"></text>
									<text class="detail-text">{{ order.appointmentTime }}</text>
								</view>
								<view class="detail-item">
									<text class="ri-car-line detail-icon"></text>
									<text class="detail-text">{{ order.carType }} · {{ order.duration }}小时</text>
								</view>
							</view>
						</view>

						<!-- 订单信息 -->
						<view class="order-meta">
							<text class="order-number">{{ order.orderNo }}</text>
							<text class="order-time">{{ order.createTime }}</text>
						</view>
					</view>

					<!-- 操作按钮区域 -->
					<view class="action-section">
						<wd-button v-if="order.status === 'pending'" type="info" size="small" plain
							@click.stop="cancelOrder(order)">
							取消订单
						</wd-button>
						<wd-button v-if="order.status === 'confirming'" type="primary" size="small"
							@click.stop="confirmOrder(order)">
							确认完成
						</wd-button>
						<wd-button v-if="order.status === 'rating'" type="primary" size="small"
							@click.stop="rateOrder(order)">
							立即评价
						</wd-button>
						<wd-button v-if="order.status === 'completed'" type="primary" size="small"
							@click.stop="rebuyOrder(order)">
							再次预约
						</wd-button>
						<wd-button type="primary" size="small" plain @click.stop="viewOrderDetail(order)">
							查看详情
						</wd-button>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-else class="empty-container">
				<image class="empty-image" src="/static/images/empty.png" mode="aspectFit"></image>
				<text class="empty-text">暂无{{ getStatusText(activeStatus) }}订单</text>
				<button class="empty-btn" @click="goToBook">立即预约</button>
			</view>
		</view>

		<!-- 底部安全区域 -->
		<view class="safe-area-bottom"></view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 页面参数
const activeStatus = ref('all') // 当前激活的状态
const activeTabIndex = ref(0) // 当前激活的tab索引
const loading = ref(false) // 加载状态

// 默认头像
const defaultAvatar = '/static/images/1.png'

// 订单状态列表
const orderStatusList = ref([
	{ name: '全部', status: 'all', count: 3 },
	{ name: '待进行', status: 'pending', count: 1 },
	{ name: '待确认', status: 'confirming', count: 0 },
	{ name: '待评价', status: 'rating', count: 2 },
	{ name: '已完成', status: 'completed', count: 0 }
])

// 订单列表数据
const orderList = ref([
	{
		id: '001',
		orderNo: 'PJ202401150001',
		createTime: '2024-01-15 10:30',
		status: 'pending',
		coachName: '张教练',
		coachAvatar: '/static/images/2.png',
		startLocation: '天安门广场',
		appointmentTime: '2024-01-16 14:00',
		carType: '大众朗逸',
		duration: 2,
		originalAmount: 200,
		actualAmount: 180
	},
	{
		id: '002',
		orderNo: 'PJ202401140002',
		createTime: '2024-01-14 16:20',
		status: 'confirming',
		coachName: '李教练',
		coachAvatar: '/static/images/2.png',
		startLocation: '王府井大街',
		appointmentTime: '2024-01-15 09:00',
		carType: '本田雅阁',
		duration: 3,
		originalAmount: 300,
		actualAmount: 280
	},
	{
		id: '003',
		orderNo: 'PJ202401130003',
		createTime: '2024-01-13 11:45',
		status: 'rating',
		coachName: '王教练',
		coachAvatar: '/static/images/2.png',
		startLocation: '三里屯',
		appointmentTime: '2024-01-14 15:30',
		carType: '丰田卡罗拉',
		duration: 2,
		originalAmount: 200,
		actualAmount: 200
	},
	{
		id: '004',
		orderNo: 'PJ202401120004',
		createTime: '2024-01-12 14:15',
		status: 'rating',
		coachName: '赵教练',
		coachAvatar: '/static/images/2.png',
		startLocation: '国贸CBD',
		appointmentTime: '2024-01-13 10:00',
		carType: '奥迪A4L',
		duration: 4,
		originalAmount: 400,
		actualAmount: 380
	},
	{
		id: '005',
		orderNo: 'PJ202401110005',
		createTime: '2024-01-11 09:30',
		status: 'completed',
		coachName: '孙教练',
		coachAvatar: '/static/images/2.png',
		startLocation: '中关村',
		appointmentTime: '2024-01-12 13:00',
		carType: '宝马3系',
		duration: 3,
		originalAmount: 350,
		actualAmount: 320
	}
])

// 页面加载时接收参数
onLoad((options) => {
	if (options.status) {
		activeStatus.value = options.status
		// 根据状态设置对应的tab索引
		const index = orderStatusList.value.findIndex(item => item.status === options.status)
		if (index !== -1) {
			activeTabIndex.value = index
		}
	}
	loadOrderList()
})

// 页面挂载
onMounted(() => {
	// 可以在这里做一些初始化操作
})

// 根据状态筛选订单
const filteredOrderList = computed(() => {
	if (activeStatus.value === 'all') {
		return orderList.value
	}
	return orderList.value.filter(order => order.status === activeStatus.value)
})

// 切换状态
const switchStatus = (status) => {
	activeStatus.value = status
	loadOrderList()
}

// 处理tab切换
const handleTabChange = ({ index }) => {
	activeTabIndex.value = index
	const status = orderStatusList.value[index].status
	activeStatus.value = status
	loadOrderList()
}

// 处理图片加载错误
const handleImageError = (e) => {
	// 图片加载失败时使用默认头像
	e.target.src = defaultAvatar
}

// 加载订单列表
const loadOrderList = async () => {
	loading.value = true
	try {
		// 模拟API请求
		await new Promise(resolve => setTimeout(resolve, 500))

		// 这里应该调用实际的API
		// const response = await uni.request({
		//     url: '/api/orders',
		//     data: { status: activeStatus.value }
		// })
		// orderList.value = response.data

		console.log('加载订单列表，状态:', activeStatus.value)
	} catch (error) {
		console.error('加载订单失败:', error)
		uni.showToast({
			title: '加载失败',
			icon: 'error'
		})
	} finally {
		loading.value = false
	}
}

// 获取状态文本
const getStatusText = (status) => {
	const statusMap = {
		all: '全部',
		pending: '待进行',
		confirming: '待确认',
		rating: '待评价',
		completed: '已完成'
	}
	return statusMap[status] || '未知'
}

// 获取状态样式类
const getStatusClass = (status) => {
	return `status-${status}`
}

// 获取状态图标
const getStatusIcon = (status) => {
	const iconMap = {
		pending: 'ri-time-line',
		confirming: 'ri-check-line',
		rating: 'ri-star-line',
		completed: 'ri-checkbox-circle-line'
	}
	return iconMap[status] || 'ri-information-line'
}

// 查看订单详情
const viewOrderDetail = (order) => {
	console.log('查看订单详情:', order.id)
	uni.navigateTo({
		url: `/pagesA/orders/detail?id=${order.id}`
	})
}

// 取消订单
const cancelOrder = (order) => {
	uni.showModal({
		title: '确认取消',
		content: '确定要取消这个订单吗？',
		success: (res) => {
			if (res.confirm) {
				// 调用取消订单API
				console.log('取消订单:', order.id)
				uni.showToast({
					title: '订单已取消',
					icon: 'success'
				})
				loadOrderList()
			}
		}
	})
}

// 确认订单
const confirmOrder = (order) => {
	uni.showModal({
		title: '确认完成',
		content: '确认陪驾服务已完成？',
		success: (res) => {
			if (res.confirm) {
				// 调用确认订单API
				console.log('确认订单:', order.id)
				uni.showToast({
					title: '订单已确认',
					icon: 'success'
				})
				loadOrderList()
			}
		}
	})
}

// 评价订单
const rateOrder = (order) => {
	console.log('评价订单:', order.id)
	// 暂时使用提示，后续可以创建评价页面
	uni.showToast({
		title: '评价功能开发中',
		icon: 'none'
	})
}

// 再次预约
const rebuyOrder = (order) => {
	console.log('再次预约:', order.id)
	// 跳转到预约页面
	uni.switchTab({
		url: '/pages/index/index'
	})
}

// 去预约
const goToBook = () => {
	uni.switchTab({
		url: '/pages/index/index'
	})
}

// 下拉刷新
const onPullDownRefresh = () => {
	loadOrderList().finally(() => {
		uni.stopPullDownRefresh()
	})
}

// 上拉加载更多
const onReachBottom = () => {
	// 这里可以实现分页加载
	console.log('触底加载更多')
}

// 导出页面生命周期函数
defineExpose({
	onPullDownRefresh,
	onReachBottom
})
</script>
<style lang="scss">
/* 页面级别样式 - 不使用 scoped */
page {
	background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
	height: 100%;
	--primary-color: #FF9500;
	--wot-color-theme: #FF9500;
	--price-color: #FF9500;
}
</style>

<style lang="scss" scoped>
/* 组件样式 - 使用 scoped */
.orders-page {
	min-height: 100vh;
}

/* tabs容器 */
.tabs-container {
	background: #fff;
	position: sticky;
	top: 0;
	z-index: 10;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
	padding-top: 10rpx;
}





/* 订单容器 */
.orders-container {
	padding: 24rpx 32rpx;
	padding-bottom: 120rpx;
	background: transparent;
	/* 让容器背景透明，显示页面背景 */
}

/* 加载状态 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 0;
}

.loading-text {
	margin-top: 24rpx;
	font-size: 28rpx;
	color: #999;
}

/* 订单列表 */
.order-list {
	display: flex;
	flex-direction: column;
	gap: 32rpx;
}

/* 订单卡片 */
.order-card {
	background: #fff;
	border-radius: 20rpx;
	margin-bottom: 32rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	border: none;
	transition: all 0.3s ease;
	overflow: hidden;
	position: relative;
}

.order-card:active {
	transform: translateY(-2rpx);
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}



/* 订单内容区域 */
.order-content {
	padding: 32rpx;
}

/* 教练信息区域 */
.coach-section {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
	position: relative;
}

.coach-avatar {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50rpx;
	margin-right: 24rpx;
	flex-shrink: 0;
	border: 3rpx solid #fff;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	transition: transform 0.3s ease;
}

.coach-avatar:active {
	transform: scale(0.95);
}

.coach-info {
	flex: 1;
}

.coach-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #1a1a1a;
	margin-bottom: 6rpx;
	letter-spacing: 0.5rpx;
}

.coach-rating {
	display: flex;
	align-items: center;
	gap: 6rpx;
}

.rating-stars {
	font-size: 24rpx;
	color: #ffc107;
	letter-spacing: 1rpx;
}

.rating-score {
	font-size: 24rpx;
	color: #666;
	font-weight: 500;
}

/* 价格显示区域 */
.price-display {
	text-align: right;
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 8rpx;
}

/* 状态指示器 */
.status-indicator {
	display: flex;
	align-items: center;
	gap: 6rpx;
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	background: rgba(0, 0, 0, 0.04);
	margin-bottom: 8rpx;
}

.status-icon {
	font-size: 24rpx;
	color: #666;
}

.status-text {
	font-size: 20rpx;
	color: #666;
	font-weight: 500;
}

/* 价格信息 */
.price-info {
	display: flex;
	align-items: baseline;
}

.price-symbol {
	font-size: 26rpx;
	color: #FF9500;
	font-weight: 600;
}

.price-number {
	font-size: 40rpx;
	color: #FF9500;
	font-weight: 700;
	font-family: 'DIN Alternate', 'Helvetica Neue', sans-serif;
}

/* 陪驾详情区域 */
.driving-details {
	margin-bottom: 24rpx;
}

.detail-row {
	display: flex;
	margin-bottom: 16rpx;
}

.detail-row:last-child {
	margin-bottom: 0;
}

.detail-item {
	display: flex;
	align-items: center;
	flex: 1;
	gap: 12rpx;
}

.detail-icon {
	font-size: 30rpx;
	width: 30rpx;
	color: #888;
	margin-right: 0;
}

.detail-text {
	font-size: 26rpx;
	color: #555;
	flex: 1;
	line-height: 1.4;
}

/* 订单信息 */
.order-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 20rpx;
	border-top: 1rpx solid #f5f5f5;
	margin-top: 8rpx;
}

.order-number {
	font-size: 22rpx;
	color: #aaa;
	font-weight: 400;
}

.order-time {
	font-size: 22rpx;
	color: #aaa;
	font-weight: 400;
}

/* 状态样式 - 现在只用于特殊状态的微调 */
.status-confirming .status-icon,
.status-confirming .status-text {
	color: #FF9500;
}

.status-confirming {
	background: rgba(255, 149, 0, 0.08);
}



/* 价格区域 */
.price-section {
	margin: 32rpx 0 24rpx 0;
	padding-top: 24rpx;
	border-top: 1rpx solid #f0f0f0;
}

.price-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.price-label {
	font-size: 28rpx;
	color: #666;
	font-weight: 500;
}

.price-amount {
	font-size: 40rpx;
	font-weight: 700;
	color: #ff4757;
	font-family: 'DIN Alternate', 'Helvetica Neue', sans-serif;
}

/* 操作按钮区域 */
.action-section {
	padding: 24rpx 32rpx;
	background: #f8f9fa;
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	gap: 16rpx;
	justify-content: flex-end;
	align-items: center;
}



/* 空状态 */
.empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 0;
	background: rgba(255, 255, 255, 0.6);
	border-radius: 20rpx;
	margin: 40rpx 0;
	backdrop-filter: blur(10rpx);
}

.empty-image {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 32rpx;
	opacity: 0.7;
	filter: grayscale(20%);
}

.empty-text {
	font-size: 32rpx;
	color: #666;
	margin-bottom: 48rpx;
	font-weight: 500;
}

.empty-btn {
	padding: 24rpx 48rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	border-radius: 48rpx;
	font-size: 32rpx;
	font-weight: 500;
	border: none;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.empty-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

/* 底部安全区域 */
.safe-area-bottom {
	height: env(safe-area-inset-bottom);
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	.orders-container {
		padding: 16rpx 24rpx;
	}

	.order-item {
		padding: 24rpx;
	}

	.order-actions {
		flex-wrap: wrap;
		gap: 12rpx;
	}

	.action-btn {
		flex: 1;
		min-width: 120rpx;
	}
}
</style>