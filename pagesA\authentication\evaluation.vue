<template>
	<view class="evaluation-container">
		<!-- 师傅信息卡片 -->
		<view class="coach-card">
			<view class="coach-avatar">
				<image src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face" mode="aspectFill"></image>
			</view>

			<view class="rating-section">
				<view class="main-rating">
					<text class="rating-score">4.16</text>
					<text class="rating-label">综合评分</text>
					<view class="rating-stars">
						<text class="ri-star-fill" v-for="n in 4" :key="n"></text>
						<text class="ri-star-line"></text>
					</view>
				</view>
			</view>
		</view>

		<!-- 评分详情 -->
		<view class="rating-details">
			<view class="rating-item">
				<view class="rating-icon">
					<text class="ri-heart-line"></text>
				</view>
				<text class="rating-category">服务态度</text>
				<text class="rating-value">4.16</text>
			</view>
			<view class="rating-item">
				<view class="rating-icon">
					<text class="ri-book-open-line"></text>
				</view>
				<text class="rating-category">教学能力</text>
				<text class="rating-value">4.16</text>
			</view>
			<view class="rating-item">
				<view class="rating-icon">
					<text class="ri-car-line"></text>
				</view>
				<text class="rating-category">车辆车况</text>
				<text class="rating-value">4.16</text>
			</view>
			<view class="rating-item">
				<view class="rating-icon">
					<text class="ri-time-line"></text>
				</view>
				<text class="rating-category">准时到达</text>
				<text class="rating-value">4.16</text>
			</view>
		</view>

		<!-- 评价列表 -->
		<view class="evaluation-list">
			<view class="evaluation-item" v-for="(evaluation, index) in evaluationList" :key="index">
				<view class="evaluation-header">
					<view class="user-avatar">
						<image :src="evaluation.avatar" mode="aspectFill"></image>
					</view>
					<view class="user-info">
						<view class="user-name">{{ evaluation.userName }}</view>
						<view class="evaluation-date">{{ evaluation.date }}</view>
					</view>
					<view class="user-rating">
						<text class="rating-text">评价</text>
						<view class="rating-stars">
							<text class="ri-star-fill" v-for="n in evaluation.rating" :key="n"></text>
							<text class="ri-star-line" v-for="n in (5 - evaluation.rating)" :key="'empty-' + n"></text>
						</view>
					</view>
				</view>

				<!-- 学习内容标签 -->
				<view class="learning-content" v-if="evaluation.learningContent">
					<view class="content-header">
						<text class="ri-book-open-line"></text>
						<text class="content-title">{{ evaluation.contentTitle }}</text>
					</view>
					<view class="content-tags">
						<view class="content-tag" v-for="(tag, tagIndex) in evaluation.learningContent" :key="tagIndex">
							<text class="tag-text">{{ tag.name }}</text>
							<text class="tag-status">{{ tag.status }}</text>
						</view>
					</view>
				</view>

				<!-- 评价内容 -->
				<view class="evaluation-content" v-if="evaluation.content">
					{{ evaluation.content }}
				</view>

				<!-- 评价图片 -->
				<view class="evaluation-images" v-if="evaluation.images && evaluation.images.length > 0">
					<image v-for="(img, imgIndex) in evaluation.images" :key="imgIndex"
						:src="img" mode="aspectFill" class="evaluation-img"></image>
				</view>
			</view>
		</view>
	</view>
</template>
<script setup>
import { ref, onMounted } from 'vue'

// 页面参数
const coachId = ref('')
const coachName = ref('')

// 评价数据
const evaluationList = ref([
	{
		userName: '于先生',
		avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
		date: '2025-07-22 20:59:28',
		rating: 4,
		contentTitle: '我今日学习内容',
		learningContent: [
			{ name: '普通道路教学', status: '一般掌握' },
			{ name: '适应你客户制', status: '一般掌握' },
			{ name: '练习技巧', status: '一般掌握' },
			{ name: '车内功能讲解-初步认识', status: '一般掌握' },
			{ name: '交通规则普及', status: '一般掌握' },
			{ name: '安全训练', status: '一般掌握' },
			{ name: '紧急停车练习', status: '一般掌握' },
			{ name: '转弯练习', status: '一般掌握' },
			{ name: '路况练习', status: '一般掌握' },
			{ name: '网方记忆车专项练习', status: '一般掌握' }
		],
		content: '',
		images: []
	},
	{
		userName: '金家明',
		avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
		date: '2025-07-22 12:03:23',
		rating: 4,
		contentTitle: '我今日学习内容',
		learningContent: [
			{ name: '普通道路教学', status: '熟练掌握' }
		],
		content: '师傅很专业，教学很耐心，让我学到了很多实用的驾驶技巧。',
		images: []
	}
])

// 返回上一页
const goBack = () => {
	uni.navigateBack()
}

// 页面加载
onMounted(() => {
	// 获取页面参数
	const pages = getCurrentPages()
	const currentPage = pages[pages.length - 1]
	coachId.value = currentPage.options.coachId || ''
	coachName.value = decodeURIComponent(currentPage.options.coachName || '')
})
</script>
<style lang="scss" scoped>
.evaluation-container {
	background: linear-gradient(180deg, #eef2ff 0%, #f7f9ff 40%, #ffffff 100%);
	min-height: 100vh;
	padding: 20rpx;
	position: relative;
}

/* 师傅信息卡片 */
.coach-card {
	background: linear-gradient(135deg,
		rgba(255, 248, 240, 0.95) 0%,
		rgba(255, 251, 245, 0.95) 50%,
		rgba(248, 250, 255, 0.95) 100%);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 24rpx;
	padding: 40rpx;
	margin-bottom: 20rpx;
	box-shadow:
		0 8rpx 32rpx rgba(0, 0, 0, 0.08),
		0 2rpx 8rpx rgba(0, 0, 0, 0.04),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
	position: relative;
	text-align: center;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg,
			rgba(255, 149, 0, 0.03) 0%,
			rgba(255, 149, 0, 0.01) 50%,
			rgba(99, 102, 241, 0.01) 100%);
		border-radius: 24rpx;
		pointer-events: none;
	}
}

.coach-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	overflow: hidden;
	margin: 0 auto 20rpx;
	position: relative;
	border: 3rpx solid rgba(255, 255, 255, 0.8);
	box-shadow:
		0 4rpx 16rpx rgba(0, 0, 0, 0.1),
		0 0 0 1rpx rgba(255, 149, 0, 0.1);
}

.coach-avatar image {
	width: 100%;
	height: 100%;
}

.rating-section {
	text-align: center;
}

.main-rating {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}

.rating-score {
	font-size: 60rpx;
	font-weight: 700;
	color: #FF9500;
	line-height: 1;
}

.rating-label {
	font-size: 24rpx;
	color: #666;
	font-weight: 400;
}

.rating-stars {
	display: flex;
	gap: 4rpx;
	justify-content: center;
}

.rating-stars .ri-star-fill {
	font-size: 24rpx;
	color: #FFD700;
}

.rating-stars .ri-star-line {
	font-size: 24rpx;
	color: #ddd;
}

/* 评分详情 */
.rating-details {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(245, 248, 255, 0.95) 100%);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 24rpx;
	padding: 36rpx;
	margin-bottom: 20rpx;
	box-shadow:
		0 8rpx 32rpx rgba(0, 0, 0, 0.08),
		0 2rpx 8rpx rgba(0, 0, 0, 0.04),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 28rpx;
	position: relative;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba(99, 102, 241, 0.03) 0%, rgba(139, 92, 246, 0.02) 100%);
		border-radius: 24rpx;
		pointer-events: none;
	}
}

.rating-item {
	text-align: center;
	transition: transform 0.3s ease;

	&:hover {
		transform: translateY(-2rpx);
	}
}

.rating-icon {
	width: 64rpx;
	height: 64rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #64748b 0%, #475569 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 14rpx;
	box-shadow: 0 4rpx 16rpx rgba(100, 116, 139, 0.2);
}

.rating-icon text {
	font-size: 30rpx;
	color: #ffffff;
}

.rating-category {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
	font-weight: 500;
}

.rating-value {
	font-size: 34rpx;
	font-weight: 700;
	color: #64748b;
    margin-left: 8rpx;
}

/* 评价列表 */
.evaluation-list {
	padding-bottom: 40rpx;
}

.evaluation-item {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(252, 252, 255, 0.95) 100%);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 24rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow:
		0 8rpx 32rpx rgba(0, 0, 0, 0.08),
		0 2rpx 8rpx rgba(0, 0, 0, 0.04),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
	transition: all 0.3s ease;
	position: relative;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba(168, 85, 247, 0.02) 0%, rgba(236, 72, 153, 0.01) 100%);
		border-radius: 24rpx;
		pointer-events: none;
		opacity: 0;
		transition: opacity 0.3s ease;
	}

	&:hover {
		transform: translateY(-4rpx);
		box-shadow:
			0 12rpx 40rpx rgba(0, 0, 0, 0.12),
			0 4rpx 12rpx rgba(0, 0, 0, 0.08),
			inset 0 1rpx 0 rgba(255, 255, 255, 0.9);

		&::before {
			opacity: 1;
		}
	}
}

.evaluation-header {
	display: flex;
	align-items: center;
	margin-bottom: 28rpx;
}

.user-avatar {
	width: 64rpx;
	height: 64rpx;
	border-radius: 50%;
	overflow: hidden;
	margin-right: 16rpx;
	border: 2rpx solid rgba(255, 255, 255, 0.8);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.user-avatar image {
	width: 100%;
	height: 100%;
}

.user-info {
	flex: 1;
}

.user-name {
	font-size: 30rpx;
	font-weight: 700;
	color: #1e293b;
	margin-bottom: 6rpx;
	letter-spacing: 0.5rpx;
}

.evaluation-date {
	font-size: 24rpx;
	color: #64748b;
	font-weight: 500;
}

.user-rating {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 10rpx;
}

.rating-text {
	font-size: 24rpx;
	color: #64748b;
	font-weight: 600;
	letter-spacing: 0.5rpx;
}

/* 学习内容 */
.learning-content {
	margin-bottom: 28rpx;
}

.content-header {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-bottom: 16rpx;
}

.content-header .ri-book-open-line {
	font-size: 24rpx;
	color: #64748b;
}

.content-title {
	font-size: 26rpx;
	font-weight: 600;
	color: #1e293b;
}

.content-tags {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.content-tag {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx 20rpx;
	background: #f8fafc;
	border-radius: 12rpx;
	border-left: 4rpx solid #64748b;
	transition: all 0.3s ease;

	&:hover {
		background: #f1f5f9;
		transform: translateX(4rpx);
	}
}

.tag-text {
	font-size: 24rpx;
	color: #1e293b;
	flex: 1;
}

.tag-status {
	font-size: 22rpx;
	color: #ffffff;
	padding: 6rpx 12rpx;
	background: #FF9500;
	border-radius: 16rpx;
	font-weight: 500;
}

/* 评价内容 */
.evaluation-content {
	font-size: 26rpx;
	color: #475569;
	line-height: 1.6;
	margin-bottom: 16rpx;
	padding: 20rpx;
	background: #f8fafc;
	border-radius: 12rpx;
	border-left: 4rpx solid #64748b;
}

/* 评价图片 */
.evaluation-images {
	display: flex;
	gap: 16rpx;
	flex-wrap: wrap;
}

.evaluation-img {
	width: 120rpx;
	height: 120rpx;
	border-radius: 16rpx;
	transition: all 0.3s ease;

	&:hover {
		transform: scale(1.05);
		box-shadow: 0 8rpx 25rpx rgba(255, 149, 0, 0.2);
	}
}

/* 全局动画 */
@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(30rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.coach-card {
	animation: fadeInUp 0.6s ease-out;
}

.rating-details {
	animation: fadeInUp 0.8s ease-out;
}

.evaluation-item {
	animation: fadeInUp 1s ease-out;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
	.rating-details {
		grid-template-columns: 1fr;
		gap: 20rpx;
	}
}
</style>