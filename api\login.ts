

import { get, post, put, del } from '../utils/request';

/**
 * 无感登录接口
 * @param {string} code 
 */
export const login = (data : any) => post('/code', data);

/**
 * 手机号绑定
 * @param {string} code
 * @method POST
 */
export const mobile = (data : any) => post('/mobile', data);

/**
 获取基础信息
 * @param {string} lng lat
 * @method POST
 */
export const baseInfo = (data : any) => get('/base/info', data);


/**
 * 获取所有城市
 * @param {string}
 * @method POST
 */
export const cityList = (data : any) => get('/city/list', data);


/**
 * 轮播列表接口
 * @param {string} page limit
 * @method POST
 */
export const bannerList = (data : any) => get('/banner/list', data);


/**
 * 信息维护接口
 * @param {string} nickname  avatar  sex birthday
 * @method POST 
 */
export const infoSubmit = (data : any) => post('/info/submit', data);