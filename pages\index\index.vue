<template>
	<view class="container" @click="closeOutside">
		<!-- 顶部导航栏 -->
		<wd-navbar title="路路安汽车陪驾" safeAreaInsetTop fixed placeholder background="transparent" title-color="#333333">
			<template #left>
				<wd-drop-menu class="city-drop-menu">
					<wd-drop-menu-item value-key="area_code" label-key="area_name" v-model="selectedCityValue"
						:options="cityOptions" @change="handleCityChange" :title="selectedCityName" icon="arrow-down"
						icon-size="20px" />
				</wd-drop-menu>
			</template>
		</wd-navbar>

		<!-- 主要内容区域 -->
		<view class="content-wrapper">
			<!-- 主banner图  2:1 -->
			<swiper class="main-banner" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500"
				:circular="true" indicator-color="rgba(255, 255, 255, 0.4)" indicator-active-color="#ffffff">
				<swiper-item v-for="(item, index) in bannerLists" :key="index">
					<image class="banner-item" :src="item.pic" />
				</swiper-item>
			</swiper>

			<!-- 我的服务 -->
			<view class="card-section">
				<view class="service-container">
					<!-- 预约核销 -->
					<!-- <view class="service-item verification-card" @click="handleVerification">
						<view class="verification-left">
							<view class="icon-circle">
								<text class="ri-coupon-line li-text-45" style="color: #FF9500;"></text>
							</view>
							<view class="verification-content">
								<text class="verification-text">预约核销</text>
								<text class="verification-desc">美团/抖音订单快速核销</text>
							</view>
						</view>
						<text class="ri-arrow-right-s-line li-text-45" style="color: #CCCCCC;"></text>
					</view> -->

					<!-- 教练专区 -->
					<view class="service-item coach-card" v-if="userInfo.isCoach" @click="handleCoachPanel">
						<view class="coach-left">
							<view class="icon-circle coach-icon">
								<text class="ri-steering-2-line li-text-45" style="color: #fff;"></text>
							</view>
							<view class="coach-content">
								<text class="coach-text">教练工作台</text>
								<text class="coach-desc">订单管理 · 收入统计 · 服务面板</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 热门服务标题 -->
			<view class="section-title">
				<text>热门陪驾服务</text>
			</view>

			<!-- 课程列表 -->
			<view class="course-list">
				<view class="course-item" v-for="(item, index) in courseList" :key="index"
					@click="handleCourseSelect(item)">
					<view class="course-left">
						<image :src="item.image" class="course-image" mode="aspectFill"></image>
					</view>
					<view class="course-info">
						<view class="course-title">{{ item.title }}</view>
						<view class="course-meta">
							<view class="course-people">{{ item.people }}人已报名</view>
							<view class="course-price-row">
								<view class="course-price">
									<text class="price-symbol">¥</text>
									<text class="current-price">{{ item.price }}</text>
									<text class="original-price">¥{{ item.originalPrice }}</text>
								</view>
								<view class="book-btn">立即预约</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		computed
	} from 'vue'
	import {
		baseInfo,
		bannerList,
		cityList,
		mobile
	} from '@/api/login.ts'
	import {
		useQueue
	} from '@/uni_modules/wot-design-uni'
	import {
		onLoad
	} from "@dcloudio/uni-app"
	import tools from '@/utils/tools'

	// 使用 useQueue 来处理点击外部关闭下拉菜单
	const {
		closeOutside
	} = useQueue()

	// 用户信息
	const userInfo = ref({
		isCoach: true, // 是否是教练身份
		coachStatus: 'online' // online, offline, busy
	})

	// 当前城市基础信息
	const cityInfo = ref({})

	// 城市选择相关数据
	const selectedCityValue = ref('')
	const cityOptions = ref([])

	// 计算当前选中的城市名称
	const selectedCityName = computed(() => {
		const city = cityOptions.value.find(item => item.area_code === selectedCityValue.value)
		return city ? city.label : '选择城市'
	})

	// 轮播图数据
	const bannerLists = ref([
		// 	{
		// 	src: '/static/images/1.png',
		// }
	])

	// 课程列表数据
	const courseList = ref([{
			id: 1,
			title: '轿车陪驾2小时/SUV通用首单特惠驾驶体验课',
			people: '4339',
			price: '138',
			originalPrice: '168',
			image: '/static/images/2.png'
		},
		{
			id: 2,
			title: '轿车陪驾4小时/SUV通用上门接送停车专项课',
			people: '188',
			price: '560',
			originalPrice: '620',
			image: '/static/images/2.png'
		},
		{
			id: 3,
			title: '轿车陪驾4小时/SUV通用上门接送高速专项课',
			people: '143',
			price: '560',
			originalPrice: '620',
			image: '/static/images/2.png'
		},
		{
			id: 4,
			title: '考前突击班',
			people: '99',
			price: '399',
			originalPrice: '499',
			image: '/static/images/2.png'
		}
	])

	// 城市选择变化处理
	const handleCityChange = ({
		value,
		selectedItem
	}) => {
		console.log('选中的城市:', selectedItem)
		uni.showToast({
			title: `已切换到${selectedItem.area_name}`,
			icon: 'success'
		})
		// 这里可以添加切换城市后的业务逻辑，比如重新加载数据
	}


	// 课程选择处理
	const handleCourseSelect = (item) => {
		// 跳转到服务详情页面
		uni.navigateTo({
			url: '/pagesA/serviceDetail/index',
		});
	}

	// 核销处理
	const handleVerification = () => {
		uni.navigateTo({
			url: '/pagesA/verification/index'
		})
	}

	// 教练面板处理
	const handleCoachPanel = () => {
		uni.navigateTo({
			url: '/pagesB/panel/index'
		})
	}


	// 获取经纬度
	const getLocation = () => {
		return new Promise((resolve, reject) => {
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					console.log('获取位置成功:', res);

					resolve({
						lat: res.latitude,
						lng: res.longitude,
					});
				},
				fail: (err) => {
					console.error('获取位置失败:', err);
					reject(err);
				}
			});
		});
	}

	const getBaseInfo = (lat, lng) => {
		baseInfo({
			lat: lat,
			lng: lng
		}).then(res => {
			if (res.code == 0) {
				cityInfo.value = res.data.city
				selectedCityValue.value = res.data.city.area_code
			}
		}).catch(error => {
			console.error('获取基础信息失败:', error);
		})
	}

	const getCityList = () => {
		cityList().then(res => {
			cityOptions.value = res.data
		})
	}

	const getBannerList = () => {
		bannerList({
			page: 1,
			limit: 100
		}).then(res => {
			bannerLists.value = res.data
			console.log(bannerLists.value);
		})
	}

	onLoad(async () => {
		try {
			const {
				lat,
				lng
			} = await getLocation()
			tools.storage.set('latLng', {
				lat: lat,
				lng: lng
			})
			getBaseInfo(lat, lng)
		} catch (error) {
			uni.showModal({
				title: '提示',
				content: '为了更好为您服务，请授权位置信息!',
				success() {
					uni.reLaunch({
						url: 'pages/index/index'
					})
				}
			})
		}
		await getCityList()
		await getBannerList()

		// 判断是否手机号授权、



	})
</script>

<style>
	page {
		background: linear-gradient(180deg, #eef2ff 0%, #f7f9ff 40%, #ffffff 100%);
		--primary-color: #FF9500;
		--card-bg: #ffffff;
		--list-bg: #f9faff;
		--text-main: #333333;
		--text-secondary: #666666;
		--text-light: #999999;
		--price-color: #FF9500;
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
	}

	.container {
		min-height: 100vh;
		position: relative;
	}

	/* 城市下拉菜单样式 */
	.city-drop-menu {
		background: transparent;
		width: 160rpx !important;
		/* 固定宽度 */
	}

	/* 重写下拉菜单项的样式 */
	.city-drop-menu :deep(.wd-drop-menu-item) {
		background: transparent;
		border: none;
		padding: 0;
		height: 85rpx !important;
		/* 固定高度，减少过高的问题 */
		line-height: 85rpx !important;
		width: 160rpx !important;
		/* 固定宽度 */
		min-width: unset;
	}

	.city-drop-menu :deep(.wd-drop-menu-item__title) {
		font-size: 32rpx;
		color: #333333;
		font-weight: 500;
		width: 120rpx !important;
		/* 固定文字区域宽度 */
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		display: inline-block;
	}

	.city-drop-menu :deep(.wd-drop-menu-item__icon) {
		color: #333333;
		font-size: 24rpx;
		/* 减小图标尺寸 */
		/* margin-left: 8rpx; */
		flex-shrink: 0;
	}

	/* 下拉菜单内容区域样式 */
	.city-drop-menu :deep(.wd-drop-menu-item__content) {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		height: 100%;
	}

	:deep(.wd-drop-menu__item-title-text) {
		width: 120rpx !important;
		height: 80rpx !important;
	}

	:deep(.wd-drop-menu__item) {
		height: 85rpx !important;
		line-height: 85rpx !important;
	}

	:deep(.wd-drop-menu__arrow) {
		right: 4px !important;
	}

	.container::before {
		content: '';
		position: absolute;
		top: 0;
		right: 0;
		width: 300rpx;
		height: 300rpx;
		background-image: radial-gradient(circle, rgba(255, 149, 0, 0.08) 20%, transparent 70%);
		z-index: -1;
	}

	.content-wrapper {
		padding: 0 24rpx 120rpx;
		position: relative;
	}

	.content-wrapper::after {
		content: '';
		position: absolute;
		bottom: 200rpx;
		left: -100rpx;
		width: 400rpx;
		height: 400rpx;
		background-image: radial-gradient(circle, rgba(64, 158, 255, 0.06) 30%, transparent 70%);
		z-index: -1;
		border-radius: 50%;
	}

	.ml-3 {
		margin-left: 12rpx;
	}

	/* Banner样式 */
	.main-banner {
		width: 100%;
		height: 360rpx;
		margin: 20rpx 0;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 5rpx 25rpx rgba(0, 0, 0, 0.08);
	}

	.banner-item {
		width: 100%;
		height: 100%;
		border-radius: 16rpx;
	}

	/* 卡片区域 */
	.card-section {
		margin: 40rpx 0 30rpx;
	}

	.verification-card {
		background: var(--card-bg);
		border-radius: 12rpx;
		padding: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.04);
		border: 1rpx solid rgba(0, 0, 0, 0.03);
	}

	.verification-left {
		display: flex;
		align-items: center;
	}

	.icon-circle {
		width: 64rpx;
		height: 64rpx;
		border-radius: 50%;
		background-color: rgba(255, 149, 0, 0.1);
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 16rpx;
	}

	.verification-content {
		display: flex;
		flex-direction: column;
	}

	.verification-text {
		font-size: 28rpx;
		font-weight: 500;
		color: var(--text-main);
	}

	.verification-desc {
		font-size: 22rpx;
		color: var(--text-light);
		margin-top: 4rpx;
	}

	/* 教练卡片样式 */
	.coach-card {
		background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
		border: 2rpx solid #e2e8f0;
		border-radius: 16rpx;
		padding: 32rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		position: relative;
		overflow: hidden;
		margin-top: 30rpx;
	}

	.coach-card::before {
		content: '';
		position: absolute;
		top: -50%;
		right: -20%;
		width: 200rpx;
		height: 200rpx;
		background: radial-gradient(circle, rgba(255, 149, 0, 0.1) 30%, transparent 70%);
		border-radius: 50%;
	}

	.coach-left {
		display: flex;
		align-items: center;
		flex: 1;
		z-index: 1;
	}

	.coach-icon {
		background: rgba(255, 149, 0, 0.1) !important;
		backdrop-filter: blur(10rpx);
		border: 2rpx solid rgba(255, 149, 0, 0.2);
	}

	.coach-content {
		margin-left: 24rpx;
	}

	.coach-text {
		font-size: 32rpx;
		font-weight: 700;
		color: var(--text-main);
		display: block;
	}

	.coach-desc {
		font-size: 24rpx;
		color: var(--text-secondary);
		margin-top: 8rpx;
		display: block;
	}

	.coach-status {
		display: flex;
		align-items: center;
		gap: 16rpx;
		z-index: 1;
	}

	.status-badge {
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		font-size: 22rpx;
		font-weight: 600;
		background: #ffffff;
		color: var(--text-secondary);
		border: 2rpx solid #e2e8f0;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
	}

	.status-badge.online {
		background: #dcfce7;
		color: #059669;
		border-color: #bbf7d0;
		font-weight: 700;
	}

	.status-badge.offline {
		background: #f1f5f9;
		color: #64748b;
		border-color: #e2e8f0;
	}

	.status-badge.busy {
		background: #fef2f2;
		color: #dc2626;
		border-color: #fecaca;
		font-weight: 700;
	}

	/* 章节标题 */
	.section-title {
		margin: 20rpx 0;
		font-size: 32rpx;
		font-weight: bold;
		color: var(--text-main);
	}

	/* 课程列表 */
	.course-list {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.course-item {
		display: flex;
		background-color: var(--card-bg);
		border-radius: 12rpx;
		padding: 24rpx;
		box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.04);
		border: 1rpx solid rgba(0, 0, 0, 0.03);
		position: relative;
	}


	.course-left {
		width: 160rpx;
		height: 160rpx;
		margin-right: 20rpx;
		border-radius: 8rpx;
		overflow: hidden;
	}

	.course-image {
		width: 100%;
		height: 100%;
	}

	.course-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.course-title {
		font-size: 28rpx;
		font-weight: 500;
		color: var(--text-main);
		line-height: 1.4;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}

	.course-meta {
		margin-top: 16rpx;
	}

	.course-people {
		font-size: 24rpx;
		color: var(--text-light);
	}

	.course-price-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 12rpx;
	}

	.course-price {
		display: flex;
		align-items: baseline;
		margin-top: 8rpx;
	}

	.price-symbol {
		font-size: 24rpx;
		color: var(--price-color);
	}

	.current-price {
		font-size: 36rpx;
		font-weight: bold;
		color: #F05F3A;
		margin-right: 10rpx;
	}

	.original-price {
		font-size: 24rpx;
		color: var(--text-light);
		text-decoration: line-through;
	}

	.book-btn {
		background: linear-gradient(to right, #FF9500, #FF7D00);
		color: #ffffff;
		font-size: 26rpx;
		padding: 10rpx 24rpx;
		border-radius: 30rpx;
		font-weight: 500;
		text-align: center;
		width: auto;
		box-shadow: 0 4rpx 8rpx rgba(255, 149, 0, 0.2);
	}

	:deep(.wd-drop-menu__item-title)::after {
		background-color: #ff7d00 !important;
	}

	/* 下拉选项激活状态 */
	:deep(.wd-drop-item__option.is-active) {
		color: #ff7d00 !important;
	}

	/* 下拉选项激活状态 - 备用选择器 */
	:deep(.wd-drop-item__option--active) {
		color: #ff7d00 !important;
	}

	/* 下拉选项激活状态 - 另一种可能的类名 */
	:deep(.is-active) {
		color: #ff7d00 !important;
	}

	/* 下拉菜单选项激活状态 */
	:deep(.wd-drop-menu__item-option.is-active) {
		color: #ff7d00 !important;
	}

	/* 更具体的选择器 */
	:deep(.wd-drop-menu .wd-drop-item__option.is-active) {
		color: #ff7d00 !important;
	}
</style>