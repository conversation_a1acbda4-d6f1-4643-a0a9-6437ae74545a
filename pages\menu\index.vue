<template>
	<view class="menu-container">
		<!-- 顶部导航栏 -->
		<wd-navbar title="特惠套餐" safeAreaInsetTop fixed placeholder :show-cancel-button="false" background="transparent"
			title-color="#333333" />

		<!-- 页面内容区域 -->
		<view class="content-wrapper">
			<!-- 热门套餐标题 -->
			<view class="section-title">
				<view class="title-decoration"></view>
				<text>精选时长推荐</text>
			</view>

			<!-- 引入课程列表组件 -->
			<course-list :courses="filteredCourses" @course-select="handleCourseSelect"></course-list>

			<!-- 底部提示 -->
			<view class="bottom-tip">
				<text class="tip-text">—— 已经到底啦 ——</text>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		computed
	} from 'vue'
	import CourseList from '@/components/CourseList.vue'

	// 搜索文本
	const searchText = ref('')

	// 当前筛选
	const activeFilter = ref('all')
	const setFilter = (filter) => {
		activeFilter.value = filter
	}

	// 当前分类
	const activeCategory = ref('all')
	const setCategory = (category) => {
		activeCategory.value = category
	}

	// 课程列表数据
	const courseList = ref([{
			id: 1,
			title: '轿车陪驾2小时/SUV通用首单特惠驾驶体验课',
			people: '4339',
			price: '138',
			originalPrice: '168',
			image: '/static/images/2.png',
			category: 'beginner',
			isPopular: true,
			isNew: false
		},
		{
			id: 2,
			title: '轿车陪驾4小时/SUV通用上门接送停车专项课',
			people: '188',
			price: '560',
			originalPrice: '620',
			image: '/static/images/2.png',
			category: 'parking',
			isPopular: true,
			isNew: true
		},
		{
			id: 3,
			title: '轿车陪驾4小时/SUV通用上门接送高速专项课',
			people: '143',
			price: '560',
			originalPrice: '620',
			image: '/static/images/2.png',
			category: 'highway',
			isPopular: false,
			isNew: true
		},
		{
			id: 4,
			title: '考前突击班',
			people: '99',
			price: '399',
			originalPrice: '499',
			image: '/static/images/2.png',
			category: 'exam',
			isPopular: true,
			isNew: false
		}
	])

	// 根据筛选条件过滤课程
	const filteredCourses = computed(() => {
		return courseList.value.filter(course => {
			// 搜索过滤
			if (searchText.value && !course.title.includes(searchText.value)) {
				return false
			}

			// 分类过滤
			if (activeCategory.value !== 'all' && course.category !== activeCategory.value) {
				return false
			}

			// 热门/最新过滤
			if (activeFilter.value === 'popular' && !course.isPopular) {
				return false
			}

			if (activeFilter.value === 'new' && !course.isNew) {
				return false
			}

			return true
		})
	})

	// 处理课程选择
	const handleCourseSelect = (item) => {
		uni.navigateTo({
			url: `/pages/course/detail?id=${item.id}`
		})
	}
</script>

<style lang="scss">
	page {
		background: linear-gradient(180deg, #eef2ff 0%, #f7f9ff 40%, #ffffff 100%);
		--primary-color: #FF9500;
		--card-bg: #ffffff;
		--list-bg: #f9faff;
		--text-main: #333333;
		--text-secondary: #666666;
		--text-light: #999999;
		--price-color: #FF9500;
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
	}

	.menu-container {
		min-height: 100vh;
		position: relative;
	}

	.content-wrapper {
		padding: 24rpx;
		padding-bottom: 120rpx;
	}

	/* 搜索框和筛选 */
	.search-filter-wrap {
		margin-bottom: 20rpx;
	}

	.search-box {
		height: 80rpx;
		background-color: #ffffff;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		margin-bottom: 20rpx;
	}

	.search-icon {
		color: var(--text-light);
		font-size: 36rpx;
		margin-right: 16rpx;
	}

	.search-input {
		flex: 1;
		height: 80rpx;
		font-size: 28rpx;
	}

	.filter-options {
		display: flex;
		justify-content: space-around;
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 16rpx 0;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.filter-item {
		font-size: 28rpx;
		color: var(--text-secondary);
		padding: 10rpx 30rpx;
		border-radius: 30rpx;
		position: relative;
	}

	.filter-item.active {
		color: var(--primary-color);
		font-weight: 500;
	}

	.filter-item.active::after {
		content: '';
		position: absolute;
		bottom: -6rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 16rpx;
		height: 6rpx;
		border-radius: 3rpx;
		background-color: var(--primary-color);
	}

	/* 分类滚动 */
	.category-scroll {
		white-space: nowrap;
		margin: 30rpx 0;
	}

	.category-list {
		display: inline-flex;
		padding: 10rpx 0;
	}

	.category-item {
		display: inline-block;
		padding: 16rpx 30rpx;
		font-size: 28rpx;
		color: var(--text-secondary);
		margin-right: 20rpx;
		border-radius: 40rpx;
		background-color: #ffffff;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.category-item.active {
		background: linear-gradient(to right, #FF9500, #FF7D00);
		color: #ffffff;
		font-weight: 500;
		box-shadow: 0 4rpx 10rpx rgba(255, 149, 0, 0.2);
	}

	/* 促销区域 */
	.promo-card {
		background: linear-gradient(120deg, #FF9500 0%, #FFAC38 100%);
		border-radius: 16rpx;
		padding: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.2);
	}

	.promo-content {
		color: #ffffff;
	}

	.promo-title {
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 8rpx;
	}

	.promo-desc {
		font-size: 24rpx;
		opacity: 0.9;
	}

	.promo-image {
		width: 160rpx;
		height: 120rpx;
	}

	/* 章节标题 */
	.section-title {
		margin: 20rpx 0 24rpx;
		font-size: 32rpx;
		font-weight: bold;
		color: var(--text-main);
		display: flex;
		align-items: center;
	}

	.title-decoration {
		width: 6rpx;
		height: 30rpx;
		background: linear-gradient(to bottom, #FF9500, #FF7D00);
		border-radius: 3rpx;
		margin-right: 16rpx;
	}

	/* 底部提示 */
	.bottom-tip {
		text-align: center;
		padding: 40rpx 0;
	}

	.tip-text {
		font-size: 24rpx;
		color: var(--text-light);
	}
</style>