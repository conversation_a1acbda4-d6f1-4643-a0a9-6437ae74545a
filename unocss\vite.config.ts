import { defineConfig } from 'vite';
import uni from '@dcloudio/vite-plugin-uni';

/** 此路径根据自己项目路径修改；默认为 uniapp 插件市场导入路径；*/
import viteVueUnocss, { unocss, flex, pseudo, border } from './js_sdk/a-hua-unocss';

export default defineConfig({
	plugins: [
		uni(),
		viteVueUnocss({
			/** 预设数组；默认[unocss()] */
			presets: [
				/**
				 * 默认预设；
				 * text-24、uno-text-24、xx-text-24...
				 */
				unocss(),
				/** 
				* 弹性盒速写；
				* flex-center、flex-col-center、flex-row-center...
				*/
				flex(),
				/** 
				* 伪类、伪元素预设；
				* after:text-24 after:(text-24 text-white)...
				*/
				pseudo(),
				/** 
				* 边框速写；
				* border-1 => border: 1px solid currentColor
				* border-1-red => border: 1px solid red
				* border-red => border: 1px solid red
				* ...
				* 默认配置如下；
				*/
				border({
					width: 1,
					style: 'solid',
					color: 'currentColor'
				}),

			],
			/**
			* 自定义原子化 CSS 前缀；
			* 默认配置如下；
			*/
			prefix: ['li'],
			/** 
			 * 排出检测原子化 CSS 规则；
			 * 默认配置如下；
			*/
			exclude: ['node_modules', 'uni_modules'],
			/** 主题配置 */
			theme: {
				/** 
				* 是否读取项目根目录 uni.scss 文件内容生成颜色预设；
				* 默认 false
				*/
				generator: false,

				/** 自定义颜色预设 */
				colors: {
					/** text-very-cool */
					veryCool: '#0000ff',
					brand: {
						/** bg-brand-primary */
						primary: 'hsl(var(--hue, 217) 78% 51%)',

						/** bg-brand */
						DEFAULT: '#942192'
					},
				}
			}


		})
	]
});