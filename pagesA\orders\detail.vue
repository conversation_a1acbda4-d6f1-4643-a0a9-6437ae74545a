<template>
	<view class="order-detail-page">
		<!-- 顶部状态卡片 -->
		<view class="status-card" :class="getStatusClass(orderDetail.status)">
			<view class="status-icon-container">
				<text class="status-icon" :class="getStatusIcon(orderDetail.status)"></text>
			</view>
			<view class="status-info">
				<text class="status-title">{{ getStatusText(orderDetail.status) }}</text>
				<text class="status-desc">{{ getStatusDescription(orderDetail.status) }}</text>
			</view>
		</view>

		<!-- 订单内容卡片 -->
		<view class="detail-card">
			<view class="card-header">
				<text class="card-title">订单信息</text>
			</view>

			<!-- 教练信息 -->
			<view class="coach-section">
				<image class="coach-avatar" :src="orderDetail.coachAvatar || defaultAvatar" mode="aspectFill"></image>
				<view class="coach-info">
					<view class="coach-name">{{ orderDetail.coachName }}</view>
					<view class="coach-rating">
						<text class="rating-stars">★★★★★</text>
						<text class="rating-score">4.9</text>
					</view>
				</view>
				<view class="contact-btn" @click="contactCoach">
					<text class="ri-phone-line"></text>
				</view>
			</view>

			<!-- 订单详情 -->
			<view class="detail-list">
				<view class="detail-item">
					<text class="detail-label">预约时间</text>
					<text class="detail-value">{{ orderDetail.appointmentTime }}</text>
				</view>
				<view class="detail-item">
					<text class="detail-label">预约地点</text>
					<text class="detail-value">{{ orderDetail.startLocation }}</text>
				</view>
				<view class="detail-item">
					<text class="detail-label">车型</text>
					<text class="detail-value">{{ orderDetail.carType }}</text>
				</view>
				<view class="detail-item">
					<text class="detail-label">陪驾时长</text>
					<text class="detail-value">{{ orderDetail.duration }}小时</text>
				</view>
			</view>
		</view>

		<!-- 费用信息卡片 -->
		<view class="detail-card">
			<view class="card-header">
				<text class="card-title">费用信息</text>
			</view>

			<view class="price-list">
				<view class="price-item">
					<text class="price-label">原价</text>
					<text class="price-value">¥{{ orderDetail.originalAmount }}</text>
				</view>
				<view class="price-item" v-if="orderDetail.originalAmount !== orderDetail.actualAmount">
					<text class="price-label">优惠</text>
					<text class="price-value discount">-¥{{ orderDetail.originalAmount - orderDetail.actualAmount }}</text>
				</view>
				<view class="price-item total">
					<text class="price-label">实付款</text>
					<view class="price-value-container">
						<text class="price-symbol">¥</text>
						<text class="price-number">{{ orderDetail.actualAmount }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 订单编号卡片 -->
		<view class="detail-card">
			<view class="card-header">
				<text class="card-title">订单编号</text>
			</view>

			<view class="order-number-section">
				<view class="order-number-item">
					<text class="order-number-label">订单编号</text>
					<view class="order-number-value-container">
						<text class="order-number-value">{{ orderDetail.orderNo }}</text>
						<text class="copy-btn" @click="copyOrderNumber">复制</text>
					</view>
				</view>
				<view class="order-number-item">
					<text class="order-number-label">创建时间</text>
					<text class="order-number-value">{{ orderDetail.createTime }}</text>
				</view>
			</view>
		</view>

		<!-- 底部操作按钮 -->
		<view class="action-bar">
			<!-- 待进行状态 -->
			<block v-if="orderDetail.status === 'pending'">
				<button class="action-btn secondary" @click="cancelOrder">取消订单</button>
				<button class="action-btn primary" @click="contactCoach">联系教练</button>
			</block>

			<!-- 待确认状态 -->
			<block v-if="orderDetail.status === 'confirming'">
				<button class="action-btn primary" @click="confirmOrder">确认完成</button>
			</block>

			<!-- 待评价状态 -->
			<block v-if="orderDetail.status === 'rating'">
				<button class="action-btn primary" @click="rateOrder">立即评价</button>
			</block>

			<!-- 已完成状态 -->
			<block v-if="orderDetail.status === 'completed'">
				<button class="action-btn secondary" @click="viewEvaluation">查看评价</button>
				<button class="action-btn primary" @click="rebuyOrder">再次预约</button>
			</block>
		</view>

		<!-- 底部安全区域 -->
		<view class="safe-area-bottom"></view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 默认头像
const defaultAvatar = '/static/images/1.png'

// 订单详情数据
const orderDetail = ref({
	id: '',
	orderNo: '',
	createTime: '',
	status: 'pending',
	coachName: '',
	coachAvatar: '',
	startLocation: '',
	appointmentTime: '',
	carType: '',
	duration: 0,
	originalAmount: 0,
	actualAmount: 0
})

// 页面加载
onLoad((options) => {
	if (options.id) {
		loadOrderDetail(options.id)
	}
})

// 加载订单详情
const loadOrderDetail = async (orderId) => {
	try {
		// 模拟API请求
		await new Promise(resolve => setTimeout(resolve, 500))

		// 这里应该调用实际的API
		// const response = await uni.request({
		//     url: `/api/orders/${orderId}`,
		// })
		// orderDetail.value = response.data

		// 模拟数据 - 根据订单ID获取不同状态的数据
		const mockDataMap = {
			'001': {
				id: '001',
				orderNo: 'PJ202401150001',
				createTime: '2024-01-15 10:30',
				status: 'pending',
				coachName: '张教练',
				coachAvatar: '/static/images/2.png',
				startLocation: '天安门广场',
				appointmentTime: '2024-01-16 14:00',
				carType: '大众朗逸',
				duration: 2,
				originalAmount: 200,
				actualAmount: 180
			},
			'002': {
				id: '002',
				orderNo: 'PJ202401140002',
				createTime: '2024-01-14 16:20',
				status: 'confirming',
				coachName: '李教练',
				coachAvatar: '/static/images/2.png',
				startLocation: '王府井大街',
				appointmentTime: '2024-01-15 09:00',
				carType: '本田雅阁',
				duration: 3,
				originalAmount: 300,
				actualAmount: 280
			},
			'003': {
				id: '003',
				orderNo: 'PJ202401130003',
				createTime: '2024-01-13 11:45',
				status: 'rating',
				coachName: '王教练',
				coachAvatar: '/static/images/2.png',
				startLocation: '三里屯',
				appointmentTime: '2024-01-14 15:30',
				carType: '丰田卡罗拉',
				duration: 2,
				originalAmount: 200,
				actualAmount: 200
			},
			'004': {
				id: '004',
				orderNo: 'PJ202401120004',
				createTime: '2024-01-12 14:15',
				status: 'rating',
				coachName: '赵教练',
				coachAvatar: '/static/images/2.png',
				startLocation: '国贸CBD',
				appointmentTime: '2024-01-13 10:00',
				carType: '奥迪A4L',
				duration: 4,
				originalAmount: 400,
				actualAmount: 380
			},
			'005': {
				id: '005',
				orderNo: 'PJ202401110005',
				createTime: '2024-01-11 09:30',
				status: 'completed',
				coachName: '孙教练',
				coachAvatar: '/static/images/2.png',
				startLocation: '中关村',
				appointmentTime: '2024-01-12 13:00',
				carType: '宝马3系',
				duration: 3,
				originalAmount: 350,
				actualAmount: 320
			}
		}

		orderDetail.value = mockDataMap[orderId] || mockDataMap['001']
		console.log('加载订单详情:', orderId)
	} catch (error) {
		console.error('加载订单详情失败:', error)
		uni.showToast({
			title: '加载失败',
			icon: 'error'
		})
	}
}

// 获取状态文本
const getStatusText = (status) => {
	const statusMap = {
		pending: '待进行',
		confirming: '待确认',
		rating: '待评价',
		completed: '已完成'
	}
	return statusMap[status] || '未知'
}

// 获取状态描述
const getStatusDescription = (status) => {
	const descMap = {
		pending: '您的订单已确认，请按时前往预约地点',
		confirming: '陪驾已完成，请确认服务完成情况',
		rating: '服务已完成，请对本次服务进行评价',
		completed: '订单已完成，感谢您的使用'
	}
	return descMap[status] || ''
}

// 获取状态样式类
const getStatusClass = (status) => {
	return `status-${status}`
}

// 获取状态图标
const getStatusIcon = (status) => {
	const iconMap = {
		pending: 'ri-time-line',
		confirming: 'ri-check-line',
		rating: 'ri-star-line',
		completed: 'ri-checkbox-circle-line'
	}
	return iconMap[status] || 'ri-information-line'
}

// 复制订单号
const copyOrderNumber = () => {
	uni.setClipboardData({
		data: orderDetail.value.orderNo,
		success: () => {
			uni.showToast({
				title: '复制成功',
				icon: 'success'
			})
		}
	})
}

// 联系教练
const contactCoach = () => {
	uni.showModal({
		title: '联系教练',
		content: '确定要拨打教练电话吗？',
		success: (res) => {
			if (res.confirm) {
				uni.makePhoneCall({
					phoneNumber: '10086', // 模拟电话号码
					fail: () => {
						uni.showToast({
							title: '拨打失败',
							icon: 'none'
						})
					}
				})
			}
		}
	})
}

// 取消订单
const cancelOrder = () => {
	uni.showModal({
		title: '确认取消',
		content: '确定要取消这个订单吗？',
		success: (res) => {
			if (res.confirm) {
				// 调用取消订单API
				console.log('取消订单:', orderDetail.value.id)
				uni.showToast({
					title: '订单已取消',
					icon: 'success'
				})
				// 返回上一页
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)
			}
		}
	})
}

// 确认订单
const confirmOrder = () => {
	uni.showModal({
		title: '确认完成',
		content: '确认陪驾服务已完成？',
		success: (res) => {
			if (res.confirm) {
				// 调用确认订单API
				console.log('确认订单:', orderDetail.value.id)
				uni.showToast({
					title: '订单已确认',
					icon: 'success'
				})
				// 刷新页面
				loadOrderDetail(orderDetail.value.id)
			}
		}
	})
}

// 评价订单
const rateOrder = () => {
	uni.navigateTo({
		url: `/pagesA/orders/evaluate?orderId=${orderDetail.value.id}`
	})
}

// 查看评价
const viewEvaluation = () => {
	uni.navigateTo({
		url: `/pagesA/orders/evaluation-detail?orderId=${orderDetail.value.id}`
	})
}

// 再次预约
const rebuyOrder = () => {
	uni.switchTab({
		url: '/pages/index/index'
	})
}
</script>

<style lang="scss">
/* 页面级别样式 */
page {
	background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
	height: 100%;
	--primary-color: #FF9500;
}
</style>

<style lang="scss" scoped>
.order-detail-page {
	min-height: 100vh;
	padding: 32rpx;
	padding-bottom: 200rpx;
}

/* 状态卡片 */
.status-card {
	background: linear-gradient(135deg,
		rgba(255, 255, 255, 0.95) 0%,
		rgba(248, 250, 255, 0.95) 100%);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 24rpx;
	padding: 40rpx;
	margin-bottom: 32rpx;
	display: flex;
	align-items: center;
	box-shadow:
		0 8rpx 32rpx rgba(0, 0, 0, 0.08),
		0 2rpx 8rpx rgba(0, 0, 0, 0.04);
	position: relative;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border-radius: 24rpx;
		pointer-events: none;
	}
}

.status-icon-container {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
	flex-shrink: 0;
}

.status-icon {
	font-size: 36rpx;
	color: #fff;
}

.status-info {
	flex: 1;
}

.status-title {
	font-size: 36rpx;
	font-weight: 700;
	color: #1a1a1a;
	display: block;
	margin-bottom: 8rpx;
}

.status-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.4;
	display: block;
}

/* 不同状态的样式 */
.status-pending {
	&::before {
		background: linear-gradient(135deg, rgba(255, 149, 0, 0.05) 0%, rgba(255, 149, 0, 0.02) 100%);
	}

	.status-icon-container {
		background: linear-gradient(135deg, #FF9500 0%, #ff7b00 100%);
		box-shadow: 0 4rpx 16rpx rgba(255, 149, 0, 0.3);
	}
}

.status-confirming {
	&::before {
		background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(34, 197, 94, 0.02) 100%);
	}

	.status-icon-container {
		background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
		box-shadow: 0 4rpx 16rpx rgba(34, 197, 94, 0.3);
	}
}

.status-rating {
	&::before {
		background: linear-gradient(135deg, rgba(168, 85, 247, 0.05) 0%, rgba(168, 85, 247, 0.02) 100%);
	}

	.status-icon-container {
		background: linear-gradient(135deg, #a855f7 0%, #9333ea 100%);
		box-shadow: 0 4rpx 16rpx rgba(168, 85, 247, 0.3);
	}
}

.status-completed {
	&::before {
		background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0.02) 100%);
	}

	.status-icon-container {
		background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
		box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.3);
	}
}

/* 详情卡片 */
.detail-card {
	background: linear-gradient(135deg,
		rgba(255, 255, 255, 0.95) 0%,
		rgba(248, 250, 255, 0.95) 100%);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 24rpx;
	margin-bottom: 32rpx;
	box-shadow:
		0 8rpx 32rpx rgba(0, 0, 0, 0.08),
		0 2rpx 8rpx rgba(0, 0, 0, 0.04);
	overflow: hidden;
}

.card-header {
	padding: 32rpx 32rpx 0;
	margin-bottom: 24rpx;
}

.card-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #1a1a1a;
}

/* 教练信息区域 */
.coach-section {
	display: flex;
	align-items: center;
	padding: 0 32rpx 32rpx;
	margin-bottom: 24rpx;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
}

.coach-avatar {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	margin-right: 24rpx;
	flex-shrink: 0;
	border: 3rpx solid #fff;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.coach-info {
	flex: 1;
}

.coach-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #1a1a1a;
	margin-bottom: 8rpx;
}

.coach-rating {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.rating-stars {
	font-size: 24rpx;
	color: #ffc107;
}

.rating-score {
	font-size: 24rpx;
	color: #666;
	font-weight: 500;
}

.contact-btn {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #FF9500 0%, #ff7b00 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 16rpx rgba(255, 149, 0, 0.3);

	.ri-phone-line {
		font-size: 32rpx;
		color: #fff;
	}
}

.contact-btn:active {
	transform: scale(0.95);
}

/* 详情列表 */
.detail-list {
	padding: 0 32rpx 32rpx;
}

.detail-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
}

.detail-item:last-child {
	border-bottom: none;
}

.detail-label {
	font-size: 28rpx;
	color: #666;
	font-weight: 500;
}

.detail-value {
	font-size: 28rpx;
	color: #1a1a1a;
	font-weight: 600;
	text-align: right;
	flex: 1;
	margin-left: 32rpx;
}

/* 价格列表 */
.price-list {
	padding: 0 32rpx 32rpx;
}

.price-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
}

.price-item:last-child {
	border-bottom: none;
}

.price-item.total {
	padding-top: 32rpx;
	border-top: 2rpx solid rgba(0, 0, 0, 0.08);
	margin-top: 16rpx;
}

.price-label {
	font-size: 28rpx;
	color: #666;
	font-weight: 500;
}

.price-item.total .price-label {
	font-size: 32rpx;
	color: #1a1a1a;
	font-weight: 700;
}

.price-value {
	font-size: 28rpx;
	color: #1a1a1a;
	font-weight: 600;
}

.price-value.discount {
	color: #22c55e;
}

.price-value-container {
	display: flex;
	align-items: baseline;
}

.price-symbol {
	font-size: 28rpx;
	color: #FF9500;
	font-weight: 600;
	margin-right: 4rpx;
}

.price-number {
	font-size: 40rpx;
	color: #FF9500;
	font-weight: 700;
	font-family: 'DIN Alternate', 'Helvetica Neue', sans-serif;
}

/* 订单编号区域 */
.order-number-section {
	padding: 0 32rpx 32rpx;
}

.order-number-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
}

.order-number-item:last-child {
	border-bottom: none;
}

.order-number-label {
	font-size: 28rpx;
	color: #666;
	font-weight: 500;
}

.order-number-value-container {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.order-number-value {
	font-size: 28rpx;
	color: #1a1a1a;
	font-weight: 600;
	font-family: 'Monaco', 'Consolas', monospace;
}

.copy-btn {
	font-size: 24rpx;
	color: #FF9500;
	padding: 8rpx 16rpx;
	background: rgba(255, 149, 0, 0.1);
	border-radius: 12rpx;
	font-weight: 500;
}

.copy-btn:active {
	background: rgba(255, 149, 0, 0.2);
}

/* 底部操作栏 */
.action-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(135deg,
		rgba(255, 255, 255, 0.95) 0%,
		rgba(248, 250, 255, 0.95) 100%);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-top: 1rpx solid rgba(255, 255, 255, 0.3);
	padding: 32rpx;
	padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
	display: flex;
	gap: 24rpx;
	box-shadow: 0 -4rpx 32rpx rgba(0, 0, 0, 0.08);
}

.action-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: 600;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.action-btn.primary {
	background: linear-gradient(135deg, #FF9500 0%, #ff7b00 100%);
	color: #fff;
	box-shadow: 0 8rpx 24rpx rgba(255, 149, 0, 0.3);
}

.action-btn.primary:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.4);
}

.action-btn.secondary {
	background: rgba(255, 255, 255, 0.9);
	color: #666;
	border: 2rpx solid rgba(0, 0, 0, 0.1);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.action-btn.secondary:active {
	background: rgba(255, 255, 255, 0.7);
	transform: translateY(2rpx);
}

/* 底部安全区域 */
.safe-area-bottom {
	height: env(safe-area-inset-bottom);
}
</style>