<template>
	<view class="cash-coupon-page">
		<!-- 筛选标签 -->
		<view class="filter-tabs">
			<wd-tabs v-model="activeTab" @change="handleTabChange" line-width="50rpx" line-height="4rpx"
				color="#FF3B30" sticky>
				<wd-tab title="全部" name="all"
					:badge-props="totalCoupons > 0 ? { modelValue: totalCoupons, right: '-8px' } : null">
				</wd-tab>
				<wd-tab title="未使用" name="available"
					:badge-props="availableCoupons > 0 ? { modelValue: availableCoupons, right: '-8px' } : null">
				</wd-tab>
				<wd-tab title="已使用" name="used"></wd-tab>
				<wd-tab title="已过期" name="expired"
					:badge-props="expiredCoupons > 0 ? { modelValue: expiredCoupons, right: '-8px' } : null">
				</wd-tab>
			</wd-tabs>
		</view>

		<!-- 券列表 -->
		<view class="coupon-list">
			<view v-if="loading" class="loading-container">
				<wd-loading type="outline" color="#FF3B30" />
				<text class="loading-text">加载中...</text>
			</view>

			<view v-else-if="filteredCoupons.length > 0" class="coupon-items">
				<view v-for="(coupon, index) in filteredCoupons" :key="coupon.id"
					class="cash-coupon-card" :class="getCouponClass(coupon.status)">

					<!-- 券卡片主体 -->
					<view class="coupon-main">
						<!-- 左侧金额 -->
						<view class="amount-section">
							<view class="amount-display">
								<text class="currency">¥</text>
								<text class="amount">{{ coupon.amount }}</text>
							</view>
							<view class="amount-label">现金券</view>
						</view>

						<!-- 右侧信息 -->
						<view class="coupon-info">
							<view class="coupon-header">
								<view class="coupon-title">{{ coupon.title }}</view>
								<view class="status-tag" :class="getStatusClass(coupon.status)">
									{{ getStatusText(coupon.status) }}
								</view>
							</view>

							<view class="coupon-condition">{{ coupon.condition }}</view>

							<view class="coupon-footer">
								<view class="expire-info">
									<text class="ri-time-line"></text>
									<text class="expire-text">{{ coupon.expireDate }} 到期</text>
								</view>

								<view v-if="coupon.status === 'available'" class="use-btn" @click="useCoupon(coupon)">
									立即使用
								</view>
								<view v-else-if="coupon.status === 'used'" class="used-date">
									{{ coupon.usedDate }} 已使用
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-else class="empty-container">
				<image class="empty-image" src="/static/images/empty-cash.png" mode="aspectFit"></image>
				<text class="empty-text">{{ getEmptyText() }}</text>
				<wd-button type="primary" size="small" custom-style="background: #FF3B30; border-color: #FF3B30;"
					@click="goToRecharge">
					去获取优惠券
				</wd-button>
			</view>
		</view>

		<!-- 底部安全区域 -->
		<view class="safe-area-bottom"></view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 页面数据
const loading = ref(true)
const activeTab = ref('all')

// 模拟现金券数据
const coupons = ref([
	{
		id: 1,
		title: '新用户专享券',
		amount: 50,
		condition: '满200元可用',
		status: 'available',
		expireDate: '2024-03-15',
		createDate: '2024-01-15',
		source: '新用户注册'
	},
	{
		id: 2,
		title: '充值返现券',
		amount: 20,
		condition: '满100元可用',
		status: 'available',
		expireDate: '2024-02-28',
		createDate: '2024-01-10',
		source: '充值活动'
	},
	{
		id: 3,
		title: '生日特惠券',
		amount: 100,
		condition: '满300元可用',
		status: 'used',
		expireDate: '2024-02-20',
		createDate: '2024-01-05',
		usedDate: '2024-01-18',
		source: '生日福利'
	},
	{
		id: 4,
		title: '推荐好友券',
		amount: 30,
		condition: '满150元可用',
		status: 'expired',
		expireDate: '2024-01-20',
		createDate: '2023-12-20',
		source: '推荐奖励'
	},
	{
		id: 5,
		title: '节日庆典券',
		amount: 80,
		condition: '满250元可用',
		status: 'available',
		expireDate: '2024-04-30',
		createDate: '2024-01-20',
		source: '节日活动'
	}
])

// 计算属性
const totalCoupons = computed(() => coupons.value.length)
const availableCoupons = computed(() => coupons.value.filter(c => c.status === 'available').length)
const expiredCoupons = computed(() => coupons.value.filter(c => c.status === 'expired').length)

const filteredCoupons = computed(() => {
	if (activeTab.value === 'all') return coupons.value
	return coupons.value.filter(coupon => coupon.status === activeTab.value)
})

// 方法
const handleTabChange = ({ name }) => {
	activeTab.value = name
}

const getCouponClass = (status) => {
	return `coupon-${status}`
}

const getStatusClass = (status) => {
	return `status-${status}`
}

const getStatusText = (status) => {
	const statusMap = {
		available: '未使用',
		used: '已使用',
		expired: '已过期'
	}
	return statusMap[status] || '未知'
}

const useCoupon = (coupon) => {
	uni.showModal({
		title: '使用现金券',
		content: `确定要使用这张${coupon.amount}元现金抵用券吗？`,
		success: (res) => {
			if (res.confirm) {
				// 跳转到预约页面
				uni.navigateTo({
					url: '/pages/index/index'
				})
			}
		}
	})
}

const getEmptyText = () => {
	const textMap = {
		all: '暂无现金抵用券',
		available: '暂无可用的现金券',
		used: '暂无已使用的现金券',
		expired: '暂无过期的现金券'
	}
	return textMap[activeTab.value] || '暂无数据'
}

const goToRecharge = () => {
	uni.navigateTo({
		url: '/pages/activity/index'
	})
}

// 页面加载
onMounted(() => {
	setTimeout(() => {
		loading.value = false
	}, 1000)
})
</script>

<style>
/* 页面级别样式 */
page {
	background: linear-gradient(180deg, #fff5f5 0%, #ffffff 100%);
	--primary-color: #FF3B30;
	--wot-color-theme: #FF3B30;
}
</style>

<style lang="scss" scoped>
/* 页面容器 */
.cash-coupon-page {
	min-height: 100vh;
	background: transparent;
}

/* 筛选标签 */
.filter-tabs {
	background: #fff;
	border-bottom: 1rpx solid #f0f0f0;
	position: sticky;
	top: 0;
	z-index: 999;
    padding-top:10rpx;
}

/* 券列表 */
.coupon-list {
	padding: 24rpx 32rpx 120rpx 32rpx;
}

/* 加载状态 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 0;
}

.loading-text {
	font-size: 28rpx;
	color: #999;
	margin-top: 24rpx;
}

/* 现金券卡片 - 简洁设计 */
.cash-coupon-card {
	background: #fff;
	border-radius: 16rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	overflow: hidden;
	// border-left: 6rpx solid #FF3B30;
}

/* 券卡片主体 */
.coupon-main {
	display: flex;
	align-items: center;
	padding: 32rpx;
}

/* 左侧金额区域 */
.amount-section {
	width: 140rpx;
	text-align: center;
	margin-right: 32rpx;
}

.amount-display {
	display: flex;
	align-items: baseline;
	justify-content: center;
	margin-bottom: 8rpx;
}

.currency {
	font-size: 28rpx;
	color: #FF3B30;
	font-weight: 600;
	margin-right: 4rpx;
}

.amount {
	font-size: 56rpx;
	color: #FF3B30;
	font-weight: 700;
	font-family: 'DIN Alternate', 'Helvetica Neue', sans-serif;
}

.amount-label {
	font-size: 20rpx;
	color: #999;
	background: #f8f9fa;
	padding: 4rpx 12rpx;
	border-radius: 10rpx;
	display: inline-block;
}

/* 右侧信息区域 */
.coupon-info {
	flex: 1;
}

.coupon-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 12rpx;
}

.coupon-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	flex: 1;
}

.status-tag {
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
	font-weight: 500;
}

.coupon-condition {
	font-size: 24rpx;
	color: #FF3B30;
	background: rgba(255, 59, 48, 0.08);
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	display: inline-block;
	margin-bottom: 16rpx;
}

.coupon-footer {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.expire-info {
	display: flex;
	align-items: center;
	gap: 6rpx;
	font-size: 22rpx;
	color: #999;
}

.expire-text {
	font-size: 22rpx;
	color: #999;
}

.use-btn {
	background: #FF3B30;
	color: #fff;
	padding: 12rpx 24rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 500;
}

.used-date {
	font-size: 20rpx;
	color: #ccc;
}

/* 状态标签样式 */
.status-available {
	background: rgba(52, 199, 89, 0.1);
	color: #34C759;
}

.status-used {
	background: rgba(142, 142, 147, 0.1);
	color: #8E8E93;
}

.status-expired {
	background: rgba(255, 59, 48, 0.1);
	color: #FF3B30;
}

/* 券状态样式 */
.coupon-used {
	opacity: 0.7;
	border-left-color: #ddd;
}

.coupon-used .amount,
.coupon-used .currency {
	color: #999;
}

.coupon-expired {
	opacity: 0.6;
	border-left-color: #ff4757;
}

.coupon-expired .amount,
.coupon-expired .currency {
	color: #ff4757;
	opacity: 0.7;
}

/* 空状态 */
.empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 0;
}

.empty-image {
	width: 240rpx;
	height: 240rpx;
	margin-bottom: 32rpx;
	opacity: 0.6;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
	margin-bottom: 40rpx;
}

/* 底部安全区域 */
.safe-area-bottom {
	height: env(safe-area-inset-bottom);
	background: transparent;
}


</style>