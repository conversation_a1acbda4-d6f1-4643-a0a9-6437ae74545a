<template>
	<view class="booking-container">
		<!-- 进度指示器 -->
		<view class="progress-indicator">
			<view class="progress-step active">
				<view class="step-circle">1</view>
				<text class="step-text">填写信息</text>
			</view>
			<view class="progress-line"></view>
			<view class="progress-step">
				<view class="step-circle">2</view>
				<text class="step-text">确认支付</text>
			</view>
		</view>

		<!-- 服务信息卡片 -->
		<view class="service-card">
			<view class="service-content">
				<view class="service-main">
					<view class="service-title" v-if="pageType === 'reservation'">
						预约信息
					</view>
					<view class="service-title" v-else>
						{{ formData.serviceName || '轿车陪驾2小时/SUV通用陪驾套餐' }}
					</view>

					<!-- 预约信息显示 -->
					<view class="reservation-info" v-if="pageType === 'reservation'">
						<view class="reservation-item">
							<text class="info-label">预约时间</text>
							<text class="info-value">{{ reservationInfo.date }} {{ reservationInfo.startTime }}-{{ reservationInfo.endTime }}({{ reservationInfo.duration }}小时)</text>
						</view>
						<view class="reservation-item" v-if="reservationInfo.coachName">
							<text class="info-label">指定师傅</text>
							<text class="info-value">{{ reservationInfo.coachName }}</text>
						</view>
					</view>

					<!-- 套餐标签 -->
					<view class="service-tags" v-if="pageType !== 'reservation'">
						<text class="service-tag">专业教练</text>
						<text class="service-tag">安全陪驾</text>
					</view>
				</view>
				<view class="service-icon">
					<text class="ri-car-line"></text>
				</view>
			</view>
			<view class="service-price">
				<text class="price-label">服务费用</text>
				<text class="price-value">¥{{ formData.price || '138' }}</text>
			</view>
		</view>

		<!-- 预约时间区域 - 只在套餐模式下显示 -->
		<view class="section-card" v-if="pageType !== 'reservation'">
			<view class="section-header">
				<text class="ri-calendar-line section-icon"></text>
				<text class="section-title">预约时间</text>
				<text class="required-mark">*</text>
			</view>

			<view class="time-selection">
				<!-- 预约日期 -->
				<view class="time-item">
					<view class="time-label">
						<text class="ri-calendar-2-line"></text>
						<text>预约日期</text>
					</view>
					<wd-picker size="large" v-model="formData.bookingDate" title="选择预约日期" :columns="dateOptions"
						@confirm="onDateConfirm" use-default-slot @open="onPickerOpen('date')"
						@cancel="onPickerClose('date')">
						<view class="time-picker-trigger">
							<text :class="{ 'placeholder': !formData.bookingDate }">{{ formData.bookingDate || '请选择日期'
								}}</text>
							<view class="arrow-container">
								<text class="arrow-icon" :class="{ 'arrow-up': showDatePicker }"></text>
							</view>
						</view>
					</wd-picker>
				</view>

				<!-- 开始时间 -->
				<view class="time-item">
					<view class="time-label">
						<text class="ri-time-line"></text>
						<text>开始时间</text>
					</view>
					<wd-picker size="large" v-model="formData.startTime" title="选择开始时间" :columns="timeOptions"
						@confirm="onTimeConfirm" use-default-slot @open="onPickerOpen('time')"
						@cancel="onPickerClose('time')">
						<view class="time-picker-trigger">
							<text :class="{ 'placeholder': !formData.startTime }">{{ formData.startTime || '请选择时间'
								}}</text>
							<view class="arrow-container">
								<text class="arrow-icon" :class="{ 'arrow-up': showTimePicker }"></text>
							</view>
						</view>
					</wd-picker>
				</view>
			</view>
		</view>
		<!-- 服务配置区域 -->
		<view class="section-card">
			<view class="section-header">
				<text class="ri-settings-3-line section-icon"></text>
				<text class="section-title">服务配置</text>
			</view>

			<!-- 指定师傅 -->
			<view class="config-item">
				<view class="config-label">
					<text class="ri-user-line config-icon"></text>
					<text class="config-text">指定师傅</text>
				</view>
				<view class="radio-group">
					<view class="radio-option" :class="{ 'active': formData.assignTeacher === false }"
						@click="formData.assignTeacher = false">
						<view class="radio-circle" :class="{ 'checked': formData.assignTeacher === false }">
							<view class="radio-dot" v-if="formData.assignTeacher === false"></view>
						</view>
						<text>不指定</text>
					</view>
					<view class="radio-option" :class="{ 'active': formData.assignTeacher === true }"
						@click="formData.assignTeacher = true">
						<view class="radio-circle" :class="{ 'checked': formData.assignTeacher === true }">
							<view class="radio-dot" v-if="formData.assignTeacher === true"></view>
						</view>
						<text>指定</text>
					</view>
				</view>
			</view>

			<!-- 陪驾车型 -->
			<view class="config-item">
				<view class="config-label">
					<text class="ri-car-line config-icon"></text>
					<text class="config-text">陪驾车型</text>
				</view>
				<view class="radio-group">
					<view class="radio-option" :class="{ 'active': formData.carType === 'sedan' }"
						@click="formData.carType = 'sedan'">
						<view class="radio-circle" :class="{ 'checked': formData.carType === 'sedan' }">
							<view class="radio-dot" v-if="formData.carType === 'sedan'"></view>
						</view>
						<text>轿车</text>
					</view>
					<view class="radio-option" :class="{ 'active': formData.carType === 'suv' }"
						@click="formData.carType = 'suv'">
						<view class="radio-circle" :class="{ 'checked': formData.carType === 'suv' }">
							<view class="radio-dot" v-if="formData.carType === 'suv'"></view>
						</view>
						<text>SUV</text>
					</view>
				</view>
			</view>

			<!-- 动力类型 -->
			<view class="config-item">
				<view class="config-label">
					<text class="ri-gas-station-line config-icon"></text>
					<text class="config-text">动力类型</text>
				</view>
				<view class="radio-group">
					<view class="radio-option" :class="{ 'active': formData.powerType === 'gas' }"
						@click="formData.powerType = 'gas'">
						<view class="radio-circle" :class="{ 'checked': formData.powerType === 'gas' }">
							<view class="radio-dot" v-if="formData.powerType === 'gas'"></view>
						</view>
						<text>燃油车</text>
					</view>
					<view class="radio-option" :class="{ 'active': formData.powerType === 'electric' }"
						@click="formData.powerType = 'electric'">
						<view class="radio-circle" :class="{ 'checked': formData.powerType === 'electric' }">
							<view class="radio-dot" v-if="formData.powerType === 'electric'"></view>
						</view>
						<text>电动车</text>
					</view>
					<view class="radio-option" :class="{ 'active': formData.powerType === 'any' }"
						@click="formData.powerType = 'any'">
						<view class="radio-circle" :class="{ 'checked': formData.powerType === 'any' }">
							<view class="radio-dot" v-if="formData.powerType === 'any'"></view>
						</view>
						<text>不限</text>
					</view>
				</view>
			</view>

			<!-- 车辆准备 -->
			<view class="config-item">
				<view class="config-label">
					<text class="ri-key-line config-icon"></text>
					<text class="config-text">车辆准备</text>
				</view>
				<view class="radio-group">
					<view class="radio-option" :class="{ 'active': formData.carSource === 'self' }"
						@click="formData.carSource = 'self'">
						<view class="radio-circle" :class="{ 'checked': formData.carSource === 'self' }">
							<view class="radio-dot" v-if="formData.carSource === 'self'"></view>
						</view>
						<text>用户备车</text>
					</view>
					<view class="radio-option" :class="{ 'active': formData.carSource === 'teacher' }"
						@click="formData.carSource = 'teacher'">
						<view class="radio-circle" :class="{ 'checked': formData.carSource === 'teacher' }">
							<view class="radio-dot" v-if="formData.carSource === 'teacher'"></view>
						</view>
						<text>师傅备车</text>
					</view>
				</view>
			</view>
		</view>
		<!-- 地址信息区域 -->
		<view class="section-card">
			<view class="section-header">
				<text class="ri-map-pin-line section-icon"></text>
				<text class="section-title">上门地址</text>
				<text class="required-mark">*</text>
			</view>

			<!-- 地址选择 -->
			<view class="address-selector" @click="chooseLocation">
				<view class="address-content">
					<view class="address-icon">
						<text class="ri-navigation-line"></text>
					</view>
					<view class="address-text" :class="{ 'placeholder': !formData.address }">
						<text>{{ formData.address || '点击选择上门地址' }}</text>
					</view>
				</view>
				<view class="location-btn">
					<text class="ri-crosshair-line"></text>
					<text>定位</text>
				</view>
			</view>

			<!-- 详细地址输入 -->
			<view class="address-detail">
				<view class="input-wrapper">
					<input type="text" class="detail-input" v-model="formData.addressDetail"
						placeholder="请输入详细地址（如：楼栋号、门牌号等）" maxlength="100" />
				</view>
			</view>
		</view>

		<!-- 用户信息区域 -->
		<view class="section-card user-info-section">
			<view class="section-header">
				<text class="ri-user-line section-icon"></text>
				<text class="section-title">用户信息</text>
				<text class="required-mark">*</text>
			</view>

			<view class="user-info-container">
				<!-- 用户姓名 -->
				<view class="input-group">
					<view class="input-label">
						<text class="ri-user-3-line input-icon"></text>
						<text class="input-text">用户姓名</text>
					</view>
					<view class="input-wrapper">
						<input
							type="text"
							class="user-input"
							v-model="formData.userName"
							placeholder="请输入用户姓名"
							maxlength="20"
						/>
					</view>
				</view>

				<!-- 用户手机号 -->
				<view class="input-group">
					<view class="input-label">
						<text class="ri-phone-line input-icon"></text>
						<text class="input-text">手机号码</text>
					</view>
					<view class="input-wrapper">
						<input
							type="number"
							class="user-input"
							v-model="formData.userPhone"
							placeholder="请输入手机号码"
							maxlength="11"
						/>
					</view>
				</view>

				<!-- 备注信息 -->
				<view class="input-group remark-group">
					<view class="input-label">
						<text class="ri-file-text-line input-icon"></text>
						<text class="input-text">备注信息</text>
					</view>
					<view class="remark-container">
						<view class="remark-wrapper">
							<textarea
								class="remark-textarea"
								v-model="formData.remark"
								placeholder="请填写特殊需求或备注信息（选填）"
								maxlength="200"
								auto-height
							/>
						</view>
						<view class="char-count">{{ formData.remark?.length || 0 }}/200</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作区域 -->
		<view class="bottom-actions" :class="{ 'hidden': showDatePicker || showTimePicker }">
			<view class="price-info">
				<text class="price-label">预估费用</text>
				<text class="price-value">¥{{ formData.price || '0' }}</text>
			</view>
			<view class="submit-btn" @click="submitBooking">
				<text>立即预约</text>
				<text class="ri-arrow-right-line"></text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

// 页面类型：'package' 套餐详情 | 'reservation' 预约详情
const pageType = ref('package')

// 预约信息（从师傅详情页传来的）
const reservationInfo = reactive({
	coachId: '',
	coachName: '',
	coachAvatar: '',
	date: '',
	startTime: '',
	endTime: '',
	duration: 0
})

// 表单数据
const formData = reactive({
	serviceId: '',
	serviceName: '',
	price: '',
	bookingDate: '',
	startTime: '',
	assignTeacher: false,
	carType: 'sedan',
	powerType: 'gas',
	carSource: 'self',
	address: '',
	addressDetail: '',
	userName: '',
	userPhone: '',
	remark: '',
	latitude: '',
	longitude: ''
});

// 选择器状态
const showDatePicker = ref(false);
const showTimePicker = ref(false);
const dateOptions = ref([]);
const timeOptions = ref([]);

// 生成未来15天的日期选项
const generateDateOptions = () => {
	const result = [];
	const now = new Date();
	for (let i = 0; i < 15; i++) {
		const date = new Date(now);
		date.setDate(now.getDate() + i);
		const month = date.getMonth() + 1;
		const day = date.getDate();
		const weekday = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.getDay()];
		result.push(`${month}月${day}日 ${weekday}`);
	}
	return result;
};

// 生成时间选项（8:00-18:00，每半小时一个选项）
const generateTimeOptions = () => {
	const result = [];
	for (let hour = 8; hour <= 18; hour++) {
		result.push(`${hour}:00`);
		if (hour < 18) {
			result.push(`${hour}:30`);
		}
	}
	return result;
};

// Picker 打开事件
const onPickerOpen = (type) => {
	console.log(`${type} picker opened`);
	if (type === 'date') {
		showDatePicker.value = true;
	} else if (type === 'time') {
		showTimePicker.value = true;
	}
};

// Picker 关闭事件
const onPickerClose = (type) => {
	console.log(`${type} picker closed`);
	if (type === 'date') {
		showDatePicker.value = false;
	} else if (type === 'time') {
		showTimePicker.value = false;
	}
};

// 日期确认
const onDateConfirm = ({ value }) => {
	formData.bookingDate = value;
	showDatePicker.value = false;
};

// 时间确认
const onTimeConfirm = ({ value }) => {
	formData.startTime = value;
	showTimePicker.value = false;
};

// 选择位置
const chooseLocation = () => {
	uni.chooseLocation({
		success: (res) => {
			formData.address = res.address;
			formData.latitude = res.latitude;
			formData.longitude = res.longitude;
		},
		fail: (err) => {
			console.error('选择位置失败', err);
			uni.showToast({
				title: '选择位置失败',
				icon: 'none'
			});
		}
	});
};

// 提交预约
const submitBooking = () => {
	// 表单验证
	if (!formData.bookingDate) {
		return uni.showToast({
			title: '请选择预约日期',
			icon: 'none'
		});
	}

	if (!formData.startTime) {
		return uni.showToast({
			title: '请选择开始时间',
			icon: 'none'
		});
	}

	if (!formData.address) {
		return uni.showToast({
			title: '请选择上门地点',
			icon: 'none'
		});
	}

	if (!formData.addressDetail) {
		return uni.showToast({
			title: '请输入详细地址',
			icon: 'none'
		});
	}

	if (!formData.userName) {
		return uni.showToast({
			title: '请输入用户姓名',
			icon: 'none'
		});
	}

	if (!formData.userPhone) {
		return uni.showToast({
			title: '请输入手机号码',
			icon: 'none'
		});
	}

	// 手机号格式验证
	const phoneRegex = /^1[3-9]\d{9}$/;
	if (!phoneRegex.test(formData.userPhone)) {
		return uni.showToast({
			title: '请输入正确的手机号码',
			icon: 'none'
		});
	}

	// 这里是提交预约的API调用
	// 示例：
	/*
	uni.request({
		url: 'https://api.example.com/booking',
		method: 'POST',
		data: formData,
		success: (res) => {
			if (res.data.code === 0) {
				uni.showToast({
					title: '预约成功',
					icon: 'success'
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			} else {
				uni.showToast({
					title: res.data.message || '预约失败',
					icon: 'none'
				});
			}
		},
		fail: (err) => {
			console.error('预约失败', err);
			uni.showToast({
				title: '网络异常，请稍后重试',
				icon: 'none'
			});
		}
	});
	*/

	// 开发阶段，模拟成功响应
	console.log('提交的预约数据:', formData);
	uni.showToast({
		title: '提交成功',
		icon: 'success'
	});

	// 跳转到下一步页面（如支付页面）
	setTimeout(() => {
		uni.navigateTo({
			url: '/pagesA/payment/index?orderId=123456'
		});
	}, 1500);
};

// 页面加载
onLoad((options) => {
	// 初始化日期和时间选项
	dateOptions.value = generateDateOptions();
	timeOptions.value = generateTimeOptions();

	// 判断页面类型
	if (options.type === 'reservation') {
		pageType.value = 'reservation'

		// 处理预约信息参数
		if (options.coachId) reservationInfo.coachId = options.coachId
		if (options.coachName) reservationInfo.coachName = decodeURIComponent(options.coachName)
		if (options.coachAvatar) reservationInfo.coachAvatar = decodeURIComponent(options.coachAvatar)
		if (options.date) reservationInfo.date = options.date
		if (options.startTime) reservationInfo.startTime = options.startTime
		if (options.endTime) reservationInfo.endTime = options.endTime
		if (options.duration) reservationInfo.duration = options.duration

		// 预约模式下，指定师傅默认为true
		formData.assignTeacher = true

		// 设置默认价格（可以根据时长计算）
		const hourlyRate = 69 // 每小时69元
		formData.price = (parseFloat(options.duration || 2) * hourlyRate).toString()

	} else {
		pageType.value = 'package'

		// 套餐模式的参数处理
		if (options.serviceId) {
			formData.serviceId = options.serviceId;
		}

		if (options.serviceName) {
			formData.serviceName = decodeURIComponent(options.serviceName);
		}

		if (options.price) {
			formData.price = options.price;
		}
	}
});
</script>

<style lang="scss">
page {
	background: #f5f7fd;
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
	min-height: 100vh;
}

.booking-container {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	padding: 20rpx 24rpx calc(160rpx + env(safe-area-inset-bottom)) 24rpx;
}

/* 进度指示器 */
.progress-indicator {
	display: flex;
	align-items: center;
	justify-content: center;
	background: #fff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.progress-step {
	display: flex;
	flex-direction: column;
	align-items: center;

	&.active {
		.step-circle {
			background: linear-gradient(135deg, #FF9500 0%, #FF7D00 100%);
			color: #fff;
		}

		.step-text {
			color: #FF9500;
			font-weight: 600;
		}
	}
}

.step-circle {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	background: #f5f5f5;
	color: #999;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	font-weight: 600;
	margin-bottom: 12rpx;
}

.step-text {
	font-size: 24rpx;
	color: #999;
}

.progress-line {
	width: 80rpx;
	height: 2rpx;
	background: #f5f5f5;
	margin: 0 32rpx;
}

/* 服务信息卡片 - 商品规格样式 */
.service-card {
	background: #fff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
	border: 1px solid #f0f0f0;
}

.service-content {
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	margin-bottom: 24rpx;
}

.service-main {
	flex: 1;
	margin-right: 24rpx;
}

.service-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	line-height: 1.4;
	margin-bottom: 16rpx;
}

.service-tags {
	display: flex;
	gap: 12rpx;
}

.service-tag {
	padding: 8rpx 16rpx;
	background: rgba(255, 149, 0, 0.1);
	color: #FF9500;
	font-size: 22rpx;
	border-radius: 12rpx;
	border: 1px solid rgba(255, 149, 0, 0.2);
}

/* 预约信息样式 */
.reservation-info {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.reservation-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.info-label {
	font-size: 24rpx;
	color: #666;
	min-width: 120rpx;
}

.info-value {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
	flex: 1;
}

.service-icon {
	width: 64rpx;
	height: 64rpx;
	background: linear-gradient(135deg, #FF9500 0%, #FF7D00 100%);
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;

	text {
		font-size: 32rpx;
		color: #fff;
	}
}

.service-price {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding-top: 24rpx;
	border-top: 1px solid #f5f5f5;
}

.price-label {
	font-size: 28rpx;
	color: #666;
}

.price-value {
	font-size: 36rpx;
	font-weight: 700;
	color: #FF9500;
}

/* 区域卡片 */
.section-card {
	background: #fff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.section-header {
	display: flex;
	align-items: center;
	margin-bottom: 32rpx;
}

.section-icon {
	font-size: 32rpx;
	color: #FF9500;
	margin-right: 12rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	flex: 1;
}

.required-mark {
	font-size: 32rpx;
	color: #ff4757;
	margin-left: 8rpx;
}

/* 时间选择区域 */
.time-selection {
	border-radius: 12rpx;
	overflow: hidden;
	background: #fff;
}

.time-item {
	display: flex;
	align-items: center;
	padding: 28rpx 24rpx;
	background: #fff;
	border-bottom: 1px solid #f5f5f5;

	&:last-child {
		border-bottom: none;
	}
}

.time-label {
	display: flex;
	align-items: center;
	width: 160rpx;
	flex-shrink: 0;

	text:first-child {
		font-size: 24rpx;
		color: #666;
		margin-right: 12rpx;
	}

	text:last-child {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
	}
}

.time-picker-trigger {
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex: 1;
	padding: 8rpx 0;

	text {
		font-size: 28rpx;
		color: #333;

		&.placeholder {
			color: #999;
		}
	}
}

.arrow-container {
	display: flex;
	align-items: center;
	margin-left: 16rpx;
}

.arrow-icon {
	display: inline-block;
	width: 0;
	height: 0;
	border-left: 8rpx solid transparent;
	border-right: 8rpx solid transparent;
	border-top: 8rpx solid #999;
	transition: transform 0.3s ease;

	&.arrow-up {
		transform: rotate(180deg);
	}
}

/* 配置选项区域 */
.config-item {
	margin-bottom: 32rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.config-label {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.config-icon {
	font-size: 24rpx;
	color: #666;
	margin-right: 12rpx;
}

.config-text {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.radio-group {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.radio-option {
	display: flex;
	align-items: center;
	padding: 16rpx 24rpx;
	background: #f8f9fa;
	border: 1px solid #e9ecef;
	border-radius: 20rpx;
	font-size: 26rpx;
	color: #666;
	transition: all 0.3s ease;
	cursor: pointer;

	&.active {
		background: rgba(255, 149, 0, 0.1);
		border-color: #FF9500;
		color: #FF9500;
	}

	text {
		margin-left: 8rpx;
	}
}

.radio-circle {
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	border: 2rpx solid #ddd;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&.checked {
		border-color: #FF9500;
		background: #fff;
	}
}

.radio-dot {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	background: #FF9500;
}

/* 地址选择区域 */
.address-selector {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx;
	background: #fafafa;
	border-radius: 12rpx;
	border: 1px solid #f0f0f0;
	margin-bottom: 24rpx;
	transition: all 0.3s ease;

	&:active {
		background: #f0f0f0;
	}
}

.address-content {
	display: flex;
	align-items: center;
	flex: 1;
}

.address-icon {
	width: 48rpx;
	height: 48rpx;
	background: linear-gradient(135deg, #FF9500 0%, #FF7D00 100%);
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;

	text {
		font-size: 24rpx;
		color: #fff;
	}
}

.address-text {
	font-size: 28rpx;
	color: #333;

	&.placeholder {
		color: #999;
	}
}

.location-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 12rpx 16rpx;
	background: rgba(255, 149, 0, 0.1);
	border-radius: 8rpx;

	text:first-child {
		font-size: 24rpx;
		color: #FF9500;
		margin-bottom: 4rpx;
	}

	text:last-child {
		font-size: 20rpx;
		color: #FF9500;
	}
}

.address-detail {
	margin-bottom: 16rpx;
}

.input-wrapper {
	background: #fafafa;
	border-radius: 12rpx;
	border: 1px solid #f0f0f0;
	padding: 4rpx 24rpx;
}

.detail-input {
	width: 100%;
	height: 88rpx;
	font-size: 28rpx;
	color: #333;
	background: transparent;

	&::placeholder {
		color: #999;
	}
}

/* 用户信息区域 */
.user-info-section {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-height: 0;
}

.user-info-container {
	display: flex;
	flex-direction: column;
	gap: 32rpx;
}

.input-group {
	display: flex;
	flex-direction: column;

	&.remark-group {
		flex: 1;
		min-height: 0;
	}
}

.input-label {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.input-icon {
	font-size: 24rpx;
	color: #666;
	margin-right: 12rpx;
}

.input-text {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.input-wrapper {
	background: #fafafa;
	border-radius: 12rpx;
	border: 1px solid #f0f0f0;
	padding: 4rpx 24rpx;
	transition: all 0.3s ease;

	&:focus-within {
		border-color: #FF9500;
		background: #fff;
	}
}

.user-input {
	width: 100%;
	height: 88rpx;
	font-size: 28rpx;
	color: #333;
	background: transparent;
	border: none;

	&::placeholder {
		color: #999;
	}
}

/* 备注区域样式 */
.remark-container {
	display: flex;
	flex-direction: column;
	flex: 1;
	min-height: 0;
}

.remark-wrapper {
	flex: 1;
	display: flex;
	flex-direction: column;
	background: #fafafa;
	border-radius: 12rpx;
	border: 1px solid #f0f0f0;
	padding: 24rpx;
	min-height: 160rpx;
	transition: all 0.3s ease;

	&:focus-within {
		border-color: #FF9500;
		background: #fff;
	}
}

.remark-textarea {
	flex: 1;
	width: 100%;
	min-height: 120rpx;
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
	background: transparent;
	border: none;
	resize: none;

	&::placeholder {
		color: #999;
	}
}

.char-count {
	align-self: flex-end;
	margin-top: 12rpx;
	font-size: 22rpx;
	color: #999;
}

/* 底部操作区域 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding: 24rpx;
	padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
	border-top: 1px solid #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	z-index: 100;
	box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.06);
	transition: transform 0.3s ease, opacity 0.3s ease;

	&.hidden {
		transform: translateY(100%);
		opacity: 0;
		pointer-events: none;
	}
}

.price-info {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
}

.price-label {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 4rpx;
}

.price-value {
	font-size: 36rpx;
	font-weight: 700;
	color: #FF9500;
}

.submit-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #FF9500 0%, #FF7D00 100%);
	color: #fff;
	padding: 24rpx 48rpx;
	border-radius: 48rpx;
	font-size: 32rpx;
	font-weight: 600;
	box-shadow: 0 8rpx 24rpx rgba(255, 149, 0, 0.3);
	transition: all 0.3s ease;

	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.3);
	}

	.ri-arrow-right-line {
		font-size: 28rpx;
		margin-left: 8rpx;
	}
}

/* 通用样式 */
.placeholder {
	color: #999 !important;
}

/* 响应式适配 */
@media (max-width: 375px) {
	.booking-container {
		padding-left: 16rpx;
		padding-right: 16rpx;
	}

	.section-card {
		padding: 24rpx;
	}

	.option-btn {
		padding: 16rpx 24rpx;
		font-size: 24rpx;
	}
}
</style>