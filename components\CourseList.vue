<template>
  <view class="course-list">
    <view class="course-item" v-for="(item, index) in courses" :key="index" @click="handleCourseSelect(item)">
      <view class="course-left">
        <image :src="item.image" class="course-image" mode="aspectFill"></image>
      </view>
      <view class="course-info">
        <view class="course-title">{{ item.title }}</view>
        <view class="course-meta">
          <view class="course-people">
            <text class="ri-user-line mr-1 text-gray-400"></text>
            {{ item.people }}人已报名
          </view>
          <view class="course-price-row">
            <view class="course-price">
              <text class="price-symbol">¥</text>
              <text class="current-price">{{ item.price }}</text>
              <text class="original-price">¥{{ item.originalPrice }}</text>
            </view>
            <view class="book-btn">立即预约</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CourseList',
  props: {
    courses: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    handleCourseSelect(item) {
      this.$emit('course-select', item);
    }
  }
}
</script>

<style>
/* 课程列表 */
.course-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.course-item {
  display: flex;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(0, 0, 0, 0.03);
  position: relative;
}


.course-left {
  width: 160rpx;
  height: 160rpx;
  margin-right: 24rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.course-image {
  width: 100%;
  height: 100%;
}

.course-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.course-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.course-meta {
  margin-top: 16rpx;
}

.course-people {
  font-size: 24rpx;
  color: #999999;
  display: flex;
  align-items: center;
}

.course-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}

.course-price {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 24rpx;
  color: #FF9500;
}

.current-price {
  font-size: 36rpx;
  font-weight: bold;
  color: #F05F3A;
  margin-right: 10rpx;
}

.original-price {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
}

.book-btn {
  background: linear-gradient(to right, #FF9500, #FF7D00);
  color: #ffffff;
  font-size: 26rpx;
  padding: 10rpx 24rpx;
  border-radius: 30rpx;
  font-weight: 500;
  text-align: center;
  box-shadow: 0 4rpx 8rpx rgba(255, 149, 0, 0.2);
}
</style> 